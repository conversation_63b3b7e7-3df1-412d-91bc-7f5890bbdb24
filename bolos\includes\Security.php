<?php
/**
 * Classe para gerenciar criptografia e segurança
 */
class Security {
    private static $key = null;
    private static $cipher = 'aes-256-cbc';
    private static $useOpenSSL = false;
    
    /**
     * Inicializa a chave de criptografia
     */
    public static function init() {
        // Verifica se OpenSSL está disponível
        self::$useOpenSSL = function_exists('openssl_encrypt');
        
        // Idealmente, esta chave deve estar em um arquivo de configuração seguro
        // ou em variáveis de ambiente, não hardcoded
        if (self::$key === null) {
            self::$key = getenv('ENCRYPTION_KEY') ?: 'cake_garden_secure_key_change_in_production';
        }
    }
    
    /**
     * Criptografa dados
     * 
     * @param string $data Dados a serem criptografados
     * @return string Dados criptografados em base64
     */
    public static function encrypt($data) {
        self::init();
        
        if (self::$useOpenSSL) {
            // Método OpenSSL (mais seguro)
            $ivlen = openssl_cipher_iv_length(self::$cipher);
            $iv = openssl_random_pseudo_bytes($ivlen);
            
            $encrypted = openssl_encrypt(
                $data, 
                self::$cipher, 
                self::$key, 
                OPENSSL_RAW_DATA, 
                $iv
            );
            
            // Combina IV e dados criptografados para armazenamento
            return base64_encode($iv . $encrypted);
        } else {
            // Método alternativo (menos seguro, mas funcional)
            return base64_encode(self::simpleEncrypt($data, self::$key));
        }
    }
    
    /**
     * Descriptografa dados
     * 
     * @param string $data Dados criptografados em base64
     * @return string|false Dados descriptografados ou false em caso de erro
     */
    public static function decrypt($data) {
        self::init();
        
        if (self::$useOpenSSL) {
            // Método OpenSSL
            $data = base64_decode($data);
            $ivlen = openssl_cipher_iv_length(self::$cipher);
            
            // Extrai IV e dados criptografados
            $iv = substr($data, 0, $ivlen);
            $encrypted = substr($data, $ivlen);
            
            return openssl_decrypt(
                $encrypted, 
                self::$cipher, 
                self::$key, 
                OPENSSL_RAW_DATA, 
                $iv
            );
        } else {
            // Método alternativo
            return self::simpleDecrypt(base64_decode($data), self::$key);
        }
    }
    
    /**
     * Método simples de criptografia para quando OpenSSL não está disponível
     * Nota: Este método é menos seguro que AES-256
     */
    private static function simpleEncrypt($data, $key) {
        $key = substr(hash('sha256', $key, true), 0, 32);
        $data_len = strlen($data);
        $key_len = strlen($key);
        $result = '';
        
        for ($i = 0; $i < $data_len; $i++) {
            $result .= chr(ord($data[$i]) ^ ord($key[$i % $key_len]));
        }
        
        return $result;
    }
    
    /**
     * Método simples de descriptografia para quando OpenSSL não está disponível
     */
    private static function simpleDecrypt($data, $key) {
        return self::simpleEncrypt($data, $key); // XOR é simétrico
    }
}
