<?php
include 'db.php';
// Removemos o session_start() daqui pois j<PERSON> é iniciado em db.php -> includes/init.php
require_once 'includes/header.php';
?>

    <style>
        .success-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .success-icon {
            font-size: 80px;
            color: #4CAF50;
            margin-bottom: 20px;
        }

        .success-title {
            font-family: 'Courgette', cursive;
            color: #93622B;
            font-size: 32px;
            margin-bottom: 20px;
        }

        .success-message {
            font-size: 18px;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn-home {
            display: inline-block;
            background-color: #93622B;
            color: white;
            padding: 12px 24px;
            font-size: 18px;
            border-radius: 4px;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .btn-home:hover {
            background-color: #7a5023;
        }
    </style>

    <div class="success-container">
        <i class="fas fa-check-circle success-icon"></i>
        <h1 class="success-title">Pedido Enviado com Sucesso!</h1>
        <p class="success-message">
            Obrigado por escolher a Cake Garden! Recebemos o seu pedido e entraremos em contato em breve para confirmar os detalhes.
            <br><br>
            Um email de confirmação foi enviado para o seu endereço de email.
        </p>
        <a href="index.php" class="btn-home">Voltar para a Página Inicial</a>
    </div>

<?php
require_once 'includes/footer.php';
?>
