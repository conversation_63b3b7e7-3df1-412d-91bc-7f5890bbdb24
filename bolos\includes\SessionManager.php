<?php
/**
 * Classe para gerenciar sessões seguras
 */
class SessionManager {

    /**
     * Inicia uma sessão segura
     */
    public static function startSecureSession() {
        // Verifica se a sessão já está ativa
        if (session_status() == PHP_SESSION_NONE) {
            try {
                // Configurações de segurança para sessões
                ini_set('session.cookie_httponly', 1);
                ini_set('session.use_only_cookies', 1);
                ini_set('session.cookie_lifetime', 0);

                // Em produção, ativar apenas HTTPS
                if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
                    ini_set('session.cookie_secure', 1);
                }

                // Iniciar sessão
                session_start();

                // Regenerar ID da sessão periodicamente para segurança
                if (!isset($_SESSION['last_regeneration'])) {
                    $_SESSION['last_regeneration'] = time();
                } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutos
                    session_regenerate_id(true);
                    $_SESSION['last_regeneration'] = time();
                }

            } catch (Exception $e) {
                // Se houver erro, destruir sessão corrompida e criar nova
                if (session_status() == PHP_SESSION_ACTIVE) {
                    session_destroy();
                }
                session_start();
                error_log("Sessão reiniciada devido a erro: " . $e->getMessage());
            }
        }
    }

    /**
     * Destrói a sessão de forma segura
     */
    public static function destroySession() {
        if (session_status() == PHP_SESSION_ACTIVE) {
            $_SESSION = array();

            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }

            session_destroy();
        }
    }
}
