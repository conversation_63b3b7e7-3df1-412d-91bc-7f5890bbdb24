<?php
include '../db.php';
// Removemos o session_start() daqui pois já é iniciado em db.php -> includes/init.php

// Verifica se o usuário está logado e é admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../login.php");
    exit;
}

// Busca informações dos pedidos
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM pedidos");
    $total_pedidos = $stmt->fetch()['total'];
    
    // Busca todos os pedidos com informações do cliente
    $stmt = $pdo->query("
        SELECT p.*, u.nome as cliente_nome, u.email as cliente_email
        FROM pedidos p
        LEFT JOIN utilizadores u ON p.id_utilizador = u.id_utilizador
        ORDER BY p.data_pedido DESC
    ");
    $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $total_pedidos = 0;
    $pedidos = [];
}

// Processar ações (atualizar status)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_status') {
        $id = $_POST['id'];
        $status = $_POST['status'];
        try {
            $stmt = $pdo->prepare("UPDATE pedidos SET status = ? WHERE id_pedido = ?");
            $stmt->execute([$status, $id]);
            $success_message = "Status do pedido atualizado com sucesso!";
        } catch (PDOException $e) {
            $error_message = "Erro ao atualizar status: " . $e->getMessage();
        }
        
        // Recarregar a página para atualizar os dados
        header("Location: pedidos.php");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Pedidos - Cake Garden</title>
    <link rel="stylesheet" href="../../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .pedidos-container {
            padding: 20px;
        }
        
        .pedidos-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .table tr:hover {
            background-color: #f5f5f5;
        }
        
        .status-select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .btn-update {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-update:hover {
            background-color: #218838;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pendente {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-confirmado {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-em-producao {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .status-pronto {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-entregue {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-cancelado {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-card h3 {
            color: #93622B;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .stats-card p {
            color: #666;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h2>Cake Garden</h2>
                <p>Painel Admin</p>
            </div>
            <ul class="menu">
                <li><a href="dashboard.php"><i class="fa-solid fa-gauge"></i> Dashboard</a></li>
                <li><a href="bolos.php"><i class="fa-solid fa-cake-candles"></i> Elementos Personalização</a></li>
                <li class="active"><a href="pedidos.php"><i class="fa-solid fa-shopping-cart"></i> Pedidos</a></li>
                <li><a href="clientes.php"><i class="fa-solid fa-users"></i> Clientes</a></li>
                <li><a href="confeiteiros.php"><i class="fa-solid fa-chef-hat"></i> Confeiteiros</a></li>
                <li><a href="../index.php"><i class="fa-solid fa-home"></i> Ver Site</a></li>
                <li><a href="../logout.php"><i class="fa-solid fa-sign-out-alt"></i> Sair</a></li>
            </ul>
        </div>
        
        <!-- Conteúdo principal -->
        <div class="main-content">
            <div class="header">
                <h1>Gestão de Pedidos</h1>
                <div class="user-info">
                    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                    <a href="../logout.php" class="logout-btn"><i class="fa-solid fa-sign-out-alt"></i></a>
                </div>
            </div>
            
            <div class="pedidos-container">
                <!-- Estatísticas -->
                <div class="stats-card">
                    <h3><?php echo $total_pedidos; ?></h3>
                    <p>Total de Pedidos Recebidos</p>
                </div>
                
                <!-- Mensagens de sucesso/erro -->
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>
                
                <!-- Lista de pedidos -->
                <div class="pedidos-table">
                    <h3 style="padding: 20px; margin: 0; background-color: #f8f9fa; border-bottom: 1px solid #ddd;">
                        Lista de Pedidos
                    </h3>
                    
                    <?php if (empty($pedidos)): ?>
                        <div style="padding: 20px; text-align: center; color: #666;">
                            Nenhum pedido recebido ainda.
                        </div>
                    <?php else: ?>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Cliente</th>
                                    <th>Email</th>
                                    <th>Data do Pedido</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pedidos as $pedido): ?>
                                    <tr>
                                        <td><?php echo $pedido['id_pedido']; ?></td>
                                        <td><?php echo htmlspecialchars($pedido['cliente_nome'] ?? 'Cliente não encontrado'); ?></td>
                                        <td><?php echo htmlspecialchars($pedido['cliente_email'] ?? 'N/A'); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($pedido['data_pedido'])); ?></td>
                                        <td>
                                            <span class="status-badge status-<?php echo str_replace(' ', '-', strtolower($pedido['status'] ?? 'pendente')); ?>">
                                                <?php echo ucfirst($pedido['status'] ?? 'Pendente'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="update_status">
                                                <input type="hidden" name="id" value="<?php echo $pedido['id_pedido']; ?>">
                                                <select name="status" class="status-select">
                                                    <option value="pendente" <?php echo ($pedido['status'] ?? 'pendente') === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                                                    <option value="confirmado" <?php echo ($pedido['status'] ?? '') === 'confirmado' ? 'selected' : ''; ?>>Confirmado</option>
                                                    <option value="em producao" <?php echo ($pedido['status'] ?? '') === 'em producao' ? 'selected' : ''; ?>>Em Produção</option>
                                                    <option value="pronto" <?php echo ($pedido['status'] ?? '') === 'pronto' ? 'selected' : ''; ?>>Pronto</option>
                                                    <option value="entregue" <?php echo ($pedido['status'] ?? '') === 'entregue' ? 'selected' : ''; ?>>Entregue</option>
                                                    <option value="cancelado" <?php echo ($pedido['status'] ?? '') === 'cancelado' ? 'selected' : ''; ?>>Cancelado</option>
                                                </select>
                                                <button type="submit" class="btn-update">
                                                    <i class="fa-solid fa-check"></i> Atualizar
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
