<?php
include 'db.php';
// Removemos o session_start() daqui pois já é iniciado em db.php -> includes/init.php

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Processar o formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enviar_pedido'])) {
    // Obter os dados do usuário da sessão
    $id_utilizador = $_SESSION['user_id'];
    $nome = $_SESSION['user_name'];
    $email = $_SESSION['user_email'];
    
    // Obter os dados do formulário
    $tipo_bolo = isset($_POST['tipo_bolo']) ? $_POST['tipo_bolo'] : '';
    $massa = isset($_POST['massa']) ? $_POST['massa'] : '';
    $cobertura = isset($_POST['cobertura']) ? $_POST['cobertura'] : '';
    $recheio = isset($_POST['recheio']) ? $_POST['recheio'] : '';
    $peso = isset($_POST['peso']) ? $_POST['peso'] : '';
    $formato = isset($_POST['formato']) ? $_POST['formato'] : '';
    $decoracao = isset($_POST['decoracao']) ? $_POST['decoracao'] : '';
    $adicional = isset($_POST['adicional']) ? $_POST['adicional'] : '';
    $mensagem = isset($_POST['mensagem']) ? $_POST['mensagem'] : '';
    $data_entrega = isset($_POST['data_entrega']) ? $_POST['data_entrega'] : '';
    
    // Validar dados básicos
    if (empty($tipo_bolo) || empty($massa) || empty($peso) || empty($data_entrega)) {
        header("Location: pedido_erro.php?erro=campos_obrigatorios");
        exit;
    }
    
    try {
        // Buscar nomes dos elementos selecionados
        $nomes = [];
        $precos = [];
        
        // Tipo de bolo
        if (!empty($tipo_bolo)) {
            $stmt = $pdo->prepare("SELECT nome_tipo, preco FROM tipo_bolo WHERE id_tipo = ?");
            $stmt->execute([$tipo_bolo]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $nomes['tipo_bolo'] = $result['nome_tipo'] ?? 'Não especificado';
            $precos['tipo_bolo'] = $result['preco'] ?? 0;
        }
        
        // Massa
        if (!empty($massa)) {
            $stmt = $pdo->prepare("SELECT nome FROM massas WHERE id_massa = ?");
            $stmt->execute([$massa]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $nomes['massa'] = $result['nome'] ?? 'Não especificado';
        }
        
        // Cobertura
        if (!empty($cobertura)) {
            $stmt = $pdo->prepare("SELECT nome FROM coberturas WHERE id_cobertura = ?");
            $stmt->execute([$cobertura]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $nomes['cobertura'] = $result['nome'] ?? 'Não especificado';
        }
        
        // Recheio
        if (!empty($recheio)) {
            $stmt = $pdo->prepare("SELECT nome FROM recheios WHERE id_recheio = ?");
            $stmt->execute([$recheio]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $nomes['recheio'] = $result['nome'] ?? 'Não especificado';
        }
        
        // Peso
        if (!empty($peso)) {
            $stmt = $pdo->prepare("SELECT peso, preco FROM pesos WHERE id_peso = ?");
            $stmt->execute([$peso]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $nomes['peso'] = $result['peso'] ?? 'Não especificado';
            $precos['peso'] = $result['preco'] ?? 0;
        }
        
        // Formato
        if (!empty($formato)) {
            $stmt = $pdo->prepare("SELECT nome FROM formatos WHERE id_formato = ?");
            $stmt->execute([$formato]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $nomes['formato'] = $result['nome'] ?? 'Não especificado';
        }
        
        // Decoração
        if (!empty($decoracao)) {
            $stmt = $pdo->prepare("SELECT nome_decoracao, preco FROM decoracao WHERE id_decoracao = ?");
            $stmt->execute([$decoracao]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $nomes['decoracao'] = $result['nome_decoracao'] ?? 'Não especificado';
            $precos['decoracao'] = $result['preco'] ?? 0;
        }
        
        // Adicional
        if (!empty($adicional)) {
            $stmt = $pdo->prepare("SELECT nome, preco FROM adicionais WHERE id_adicional = ?");
            $stmt->execute([$adicional]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $nomes['adicional'] = $result['nome'] ?? 'Não especificado';
            $precos['adicional'] = $result['preco'] ?? 0;
        }
        
        // Calcular preço total
        $preco_total = ($precos['tipo_bolo'] ?? 0) + ($precos['peso'] ?? 0) + ($precos['decoracao'] ?? 0) + ($precos['adicional'] ?? 0);
        
        // Obter informações do usuário
        $stmt = $pdo->prepare("SELECT telefone FROM utilizadores WHERE id_utilizador = ?");
        $stmt->execute([$id_utilizador]);
        $utilizador = $stmt->fetch(PDO::FETCH_ASSOC);
        $telefone = $utilizador['telefone'] ?? '';
        
        // Inserir o bolo
        $stmt = $pdo->prepare("INSERT INTO bolo (id_tipo, id_massa, id_cobertura, id_recheio, id_peso, id_formato, id_decoracao, id_adicional) 
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$tipo_bolo, $massa, $cobertura, $recheio, $peso, $formato, $decoracao, $adicional]);
        $id_bolo = $pdo->lastInsertId();
        
        // Inserir o pedido
        $stmt = $pdo->prepare("INSERT INTO pedidos (id_utilizador, id_bolo, data_pedido, data_entrega, mensagem, status) 
                              VALUES (?, ?, NOW(), ?, ?, 'Pendente')");
        $stmt->execute([$id_utilizador, $id_bolo, $data_entrega, $mensagem]);
        $id_pedido = $pdo->lastInsertId();
        
        // Preparar dados para o email
        $dados_email = [
            'nome_cliente' => $nome,
            'email_cliente' => $email,
            'telefone_cliente' => $telefone,
            'id_pedido' => $id_pedido,
            'tipo_bolo' => $nomes['tipo_bolo'] ?? 'Não especificado',
            'massa' => $nomes['massa'] ?? 'Não especificado',
            'cobertura' => $nomes['cobertura'] ?? 'Não especificado',
            'recheio' => $nomes['recheio'] ?? 'Não especificado',
            'peso' => $nomes['peso'] ?? 'Não especificado',
            'formato' => $nomes['formato'] ?? 'Não especificado',
            'decoracao' => $nomes['decoracao'] ?? 'Não especificado',
            'adicional' => $nomes['adicional'] ?? 'Não especificado',
            'data_entrega' => date('d/m/Y', strtotime($data_entrega)),
            'mensagem' => $mensagem,
            'preco_total' => number_format($preco_total, 2, ',', '.'),
            'data_pedido' => date('d/m/Y H:i')
        ];
        
        // Enviar email usando Static Forms com dados em português
        $email_data = [
            'apiKey' => 'sf_9a8kg7g1ghad305n8f2i3en4',
            'replyTo' => $email,
            'subject' => 'Nova Encomenda de Bolo - Cake Garden',
            
            // Dados do cliente
            'Nome_do_Cliente' => $dados_email['nome_cliente'],
            'Email_do_Cliente' => $dados_email['email_cliente'],
            'Telefone_do_Cliente' => $dados_email['telefone_cliente'],
            'Numero_do_Pedido' => $dados_email['id_pedido'],
            
            // Detalhes do bolo
            'Tipo_de_Bolo' => $dados_email['tipo_bolo'],
            'Massa' => $dados_email['massa'],
            'Cobertura' => $dados_email['cobertura'],
            'Recheio' => $dados_email['recheio'],
            'Peso' => $dados_email['peso'] . ' kg',
            'Formato' => $dados_email['formato'],
            'Decoracao' => $dados_email['decoracao'],
            'Adicionais' => $dados_email['adicional'],
            
            // Detalhes da entrega
            'Data_de_Entrega' => $dados_email['data_entrega'],
            'Instrucoes_Especiais' => $dados_email['mensagem'] ?: 'Nenhuma instrução especial',
            'Preco_Total_Estimado' => '€' . $dados_email['preco_total'],
            'Data_do_Pedido' => $dados_email['data_pedido'],
            
            'redirectTo' => 'https://example.com/pedido_sucesso.php'
        ];
        
        // Enviar via cURL para Static Forms
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.staticforms.xyz/submit');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($email_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        // Redirecionar para página de sucesso
        header("Location: pedido_sucesso.php?pedido=" . $id_pedido);
        exit;
        
    } catch (PDOException $e) {
        error_log("Erro no pedido: " . $e->getMessage());
        header("Location: pedido_erro.php?erro=database");
        exit;
    }
} else {
    // Se não foi um POST válido, redirecionar para a loja
    header("Location: loja2.php");
    exit;
}
?>

