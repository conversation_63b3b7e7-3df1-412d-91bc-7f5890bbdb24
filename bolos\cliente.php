<?php
require_once 'db.php'; // Inclui a conexão com o banco de dados

// Verificar se a tabela depoimentos existe
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'depoimentos'");
    $table_exists = $stmt->rowCount() > 0;

    if ($table_exists) {
        // Consultar depoimentos de clientes
        $query_depoimentos = $pdo->query("SELECT * FROM depoimentos WHERE status = 'aprovado' ORDER BY data DESC LIMIT 6");
        $depoimentos = $query_depoimentos->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // Redirecionar para o script de criação da tabela
        header("Location: criar_tabela_depoimentos.php");
        exit;
    }
} catch (PDOException $e) {
    // Se ocorrer um erro, usaremos dados de exemplo
    $depoimentos = [];
}

// Se não existirem depoimentos no banco, usaremos alguns exemplos
if (empty($depoimentos)) {
    $depoimentos = [
        [
            'nome' => '<PERSON>',
            'foto' => '../image/cliente1.jpg',
            'comentario' => 'O bolo de chocolate com morango que encomendei para o aniversário da minha filha estava simplesmente divino! Todos os convidados elogiaram o sabor e a decoração.',
            'data' => '2025-05-20',
            'avaliacao' => 5
        ],
        [
            'nome' => 'João Pereira',
            'foto' => '../image/cliente2.jpg',
            'comentario' => 'Encomendei um bolo para o aniversário da minha esposa e fiquei impressionado com a qualidade. A massa estava fofa e o recheio de doce de leite perfeito!',
            'data' => '2025-04-13',
            'avaliacao' => 5
        ],
        [
            'nome' => 'Ana Oliveira',
            'foto' => '../image/cliente3.jpg',
            'comentario' => 'Já experimentei vários bolos da Cake Garden e nunca me decepcionei. O atendimento é excelente e os bolos são sempre frescos e deliciosos.',
            'data' => '2025-04-05',
            'avaliacao' => 4
        ],
        [
            'nome' => 'Carlos Santos',
            'foto' => '../image/cliente4.jpg',
            'comentario' => 'O bolo de casamento que encomendamos superou todas as nossas expectativas! Além de lindo, estava delicioso e todos os convidados adoraram.',
            'data' => '2025-03-18',
            'avaliacao' => 5
        ],
        [
            'nome' => 'Mariana Costa',
            'foto' => '../image/cliente5.jpg',
            'comentario' => 'Adoro os bolos da Cake Garden! Sempre que tenho uma ocasião especial, faço questão de encomendar com eles. Nunca me decepcionaram.',
            'data' => '2025-03-12',
            'avaliacao' => 5
        ],
        [
            'nome' => 'Pedro Almeida',
            'foto' => '../image/cliente6.jpg',
            'comentario' => 'Encomendei um bolo para o aniversário da empresa e foi um sucesso! Todos elogiaram o sabor e a apresentação. Com certeza voltarei a encomendar.',
            'data' => '2025-03-05',
            'avaliacao' => 4
        ]
    ];
}
require_once 'includes/header.php';
?>

    <style>
        .clientes-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .clientes-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .clientes-header h1 {
            color: #93622B;
            font-family: 'Cormorant Garamond', serif;
            font-size: 42px;
            margin-bottom: 15px;
        }

        .clientes-header p {
            color: #666;
            font-size: 18px;
            max-width: 800px;
            margin: 0 auto;
        }

        .depoimentos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .depoimento-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
        }

        .depoimento-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .cliente-foto {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 3px solid #93622B;
        }

        .cliente-info h3 {
            color: #333;
            font-family: 'Lobster Two', cursive;
            font-size: 20px;
            margin-bottom: 5px;
        }

        .cliente-info .data {
            color: #999;
            font-size: 14px;
        }

        .avaliacao {
            color: #FFD700;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .depoimento-texto {
            color: #666;
            line-height: 1.6;
            font-style: italic;
            position: relative;
        }

        .depoimento-texto::before {
            content: '"';
            font-size: 60px;
            color: #f0f0f0;
            position: absolute;
            top: -20px;
            left: -15px;
            font-family: serif;
            z-index: -1;
        }

        .compartilhar-depoimento {
            background-color: #FFF8F0;
            padding: 40px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 50px;
        }

        .compartilhar-depoimento h2 {
            color: #93622B;
            font-family: 'Lobster Two', cursive;
            font-size: 32px;
            margin-bottom: 20px;
        }

        .compartilhar-depoimento p {
            color: #666;
            font-size: 16px;
            max-width: 700px;
            margin: 0 auto 25px;
        }

        .btn-compartilhar {
            display: inline-block;
            background-color: #93622B;
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-compartilhar:hover {
            background-color: #7A5023;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .decorative-line {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .line {
            height: 2px;
            width: 100px;
            background-color: #93622B;
            margin: 0 15px;
        }
    </style>

    <!-- fim do menu-->

    <div class="clientes-container">
        <div class="clientes-header">
            <div class="decorative-line">
                <div class="line"></div>
                <h1>Depoimentos dos Nossos Clientes</h1>
                <div class="line"></div>
            </div>
            <p>Descubra o que nossos clientes têm a dizer sobre nossas delícias e serviços</p>
        </div>

        <!-- Seção de Depoimentos -->
        <div class="depoimentos-grid">
            <?php foreach ($depoimentos as $depoimento): ?>
            <div class="depoimento-card">
                <div class="depoimento-header">
                    <img src="<?php echo $depoimento['foto']; ?>" alt="<?php echo $depoimento['nome']; ?>" class="cliente-foto">
                    <div class="cliente-info">
                        <h3><?php echo $depoimento['nome']; ?></h3>
                        <span class="data"><?php echo date('d/m/Y', strtotime($depoimento['data'])); ?></span>
                    </div>
                </div>
                <div class="avaliacao">
                    <?php
                    for ($i = 1; $i <= 5; $i++) {
                        if ($i <= $depoimento['avaliacao']) {
                            echo '<i class="fas fa-star"></i>';
                        } else {
                            echo '<i class="far fa-star"></i>';
                        }
                    }
                    ?>
                </div>
                <div class="depoimento-texto">
                    <p><?php echo $depoimento['comentario']; ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Seção para compartilhar depoimento -->
        <div class="compartilhar-depoimento">
            <h2>Compartilhe sua experiência</h2>
            <p>Adoraríamos ouvir sobre sua experiência com nossos bolos e serviços. Seu feedback é muito importante para continuarmos melhorando e oferecendo o melhor para você!</p>
            <a href="enviar-depoimento.php" class="btn-compartilhar">ENVIAR MEU DEPOIMENTO</a>
        </div>
    </div>

<?php
require_once 'includes/footer.php';
?>
