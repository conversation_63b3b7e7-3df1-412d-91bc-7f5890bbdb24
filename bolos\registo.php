<?php
    include 'db.php';
    $message = ''; // Variável para mensagens de feedback

    if ($_SERVER['REQUEST_METHOD'] === 'POST'){
        // Captura todos os campos do formulário
        $nome = $_POST['nome'] ?? '';
        $email = $_POST['email'] ?? '';
        $telefone = $_POST['telefone'] ?? '';
        $morada = $_POST['morada'] ?? '';
        $codigo_postal = $_POST['codigo_postal'] ?? '';
        $senha = $_POST['senha'] ?? '';
        $role = 'cliente'; // Por padrão, todos os registros são de clientes
        
        // Verifica se o email já existe
        $stmt = $pdo->prepare("SELECT * FROM utilizadores WHERE email = ?");
        $stmt->execute([$email]);
        $user_exists = $stmt->fetch();
        
        if ($user_exists) {
            $message = "Este email já está registrado.";
        }
        // Verifica se os campos obrigatórios foram preenchidos
        elseif (empty($nome) || empty($email) || empty($senha)) {
            $message = "Por favor, preencha todos os campos obrigatórios.";
        } else {
            try {
                // Primeiro, vamos verificar se a coluna codigo_postal existe
                $stmt = $pdo->query("DESCRIBE utilizadores");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $codigo_postal_exists = false;
                
                foreach ($columns as $column) {
                    if (strtolower($column) === 'codigo_postal') {
                        $codigo_postal_exists = true;
                        break;
                    }
                }
                
                // Se a coluna não existir, vamos inserir sem ela
                if (!$codigo_postal_exists) {
                    $stmt = $pdo->prepare("INSERT INTO utilizadores (nome, email, senha, telefone, morada, role) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $nome, 
                        $email, 
                        password_hash($senha, PASSWORD_DEFAULT), 
                        $telefone, 
                        $morada, 
                        $role
                    ]);
                } else {
                    // Se a coluna existir, incluímos ela na inserção
                    $stmt = $pdo->prepare("INSERT INTO utilizadores (nome, email, senha, telefone, morada, codigo_postal, role) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $nome, 
                        $email, 
                        password_hash($senha, PASSWORD_DEFAULT), 
                        $telefone, 
                        $morada, 
                        $codigo_postal, 
                        $role
                    ]);
                }
                
                $message = "Registo realizado com sucesso! Faça login para continuar.";
                
                // Redireciona para a página de login após 2 segundos
                header("refresh:2;url=login.php");
            } catch (PDOException $e) {
                $message = "Erro ao registrar: " . $e->getMessage();
            }
        }
    }
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registo - Cake Garden</title>
    <link rel="stylesheet" href="../css/estilo.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    
    <div class="registo">
        <div class="form-box" id="register-form">
            <form action="" method="post">
                <h2>Registo</h2>
                
                <?php if (!empty($message)): ?>
                    <p class="message <?php echo strpos($message, 'sucesso') !== false ? 'success' : 'error'; ?>">
                        <?= htmlspecialchars($message) ?>
                    </p>
                <?php endif; ?>
                
                <!-- Campo Nome -->
                <div class="input-group">
                    <i class="fa-solid fa-user"></i>
                    <input type="text" name="nome" placeholder="Nome completo" required>
                </div>
                
                <!-- Campo Email -->
                <div class="input-group">
                    <i class="fa-solid fa-envelope"></i>
                    <input type="email" name="email" placeholder="Email" required>
                </div>
                
                <!-- Campo Telefone -->
                <div class="input-group">
                    <i class="fa-solid fa-phone"></i>
                    <input type="text" name="telefone" placeholder="Telefone" required>
                </div>
                
                <!-- Campo Morada -->
                <div class="input-group">
                    <i class="fa-solid fa-home"></i>
                    <input type="text" name="morada" placeholder="Morada" required>
                </div>
                
                <!-- Campo Código Postal -->
                <div class="input-group">
                    <i class="fa-solid fa-map-pin"></i>
                    <input type="text" name="codigo_postal" placeholder="Código-Postal" required>
                </div>
                
                <!-- Campo Senha -->
                <div class="input-group">
                    <i class="fa-solid fa-lock"></i>
                    <input type="password" name="senha" placeholder="Senha" required>
                </div>
                
                <button type="submit" class="btn">Cadastrar</button>
                <p>Já tem conta? <a href="login.php">Login</a></p>
            </form>
        </div>
    </div>
</body>

