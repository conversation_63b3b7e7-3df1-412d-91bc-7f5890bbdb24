<?php
    include 'db.php';
    require_once 'includes/header.php';
?>

    <!-- Complete form with reCAPTCHA and custom fields -->
<form action="https://api.staticforms.xyz/submit" method="POST">
  <!-- Required: Your Static Forms API key -->
  <input type="hidden" name="apiKey" value="sf_9a8kg7g1ghad305n8f2i3en4">

  <!-- Enable reply-to functionality -->
  <input type="hidden" name="replyTo" value="@">

  <!-- Anti-spam honeypot field -->
  <input
    type="text"
    name="honeypot"
    style="display: none"
    tabindex="-1"
    autocomplete="off"
  >
    <header>
        <div class="about-us">

            <div class="decorative-line">
            <div class="line"></div>
              <h1>FALE CONNOSCO</h1>
            <div class="line"></div>

        </div>
        <!--formulário de contacto-->
        <Section class="contacto">
            <div class="contact-container">
                <div class="contact-column">
                    <h3>Hor<PERSON><PERSON> de funcionamento</h3>
                    <p>Segunda a sexta: 8:00 - 18:00</p>
                    <p>Sábado: 9:00 - 15:00</p>
                    <p>Domingo: Fechado</p>

                    <!-- Aviso de antecedência para encomendas -->
                    <div class="aviso-encomenda">
                        <i class="fa-solid fa-circle-exclamation"></i>
                        <h4>Aviso Importante</h4>
                        <p>Todas as encomendas devem ser feitas com pelo menos 2 dias de antecedência para garantirmos a qualidade e personalização dos nossos produtos.</p>
                    </div>
                </div>

                <!-- Formulário de contato usando Static Forms -->
                <div class="contact-column">
                    <h3>Envie mensagem</h3>
                    <form class="form" action="https://api.staticforms.xyz/submit" method="POST">
                        <!-- Required: Your Static Forms API key -->
                        <input type="hidden" name="accessKey" value="sf_9a8kg7g1ghad305n8f2i3en4">

                        <!-- Redirect URL after form submission -->
                        <input type="hidden" name="redirectTo" value="http://localhost/teste%201/bolos/obrigado.php">

                        <!-- Subject line for the email -->
                        <input type="hidden" name="subject" value="Novo contato do site Cake Garden">

                        <!-- Enable reply-to functionality - Corrigido -->
                        <!-- O campo replyTo será preenchido automaticamente com o email do remetente -->

                        <!-- Anti-spam honeypot field -->
                        <input type="text" name="honeypot" style="display: none" tabindex="-1" autocomplete="off">

                        <!-- Form fields - Certifique-se de que os nomes dos campos estão corretos -->
                        <input class="field" name="name" placeholder="Nome" required>
                        <input class="field" name="email" placeholder="E-mail" required>
                        <textarea name="message" class="message" placeholder="Digite sua mensagem aqui" required></textarea>

                        <div class="button-container">
                            <button type="submit">Enviar</button>
                        </div>
                    </form>
                </div>

                <div class="contact-column">
                    <h3>Nossos contactos</h3>
                    <p><i class="fa-solid fa-mobile"></i> +351 912 345 678</p>
                    <p><i class="fa-solid fa-envelope"></i> <EMAIL></p>
                    <p><i class="fa-solid fa-location-dot"></i> Av. da Liberdade, 123, Lisboa</p>
                </div>
            </div>
        </Section>

<?php
require_once 'includes/footer.php';
?>
