<?php
include '../db.php';
// Removemos o session_start() daqui pois já é iniciado em db.php -> includes/init.php

// Verifica se o usuário está logado e é admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../login.php");
    exit;
}

// Busca informações dos confeiteiros
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM utilizadores WHERE role = 'confeiteiro'");
    $total_confeiteiros = $stmt->fetch()['total'];
    
    // Busca todos os confeiteiros
    $stmt = $pdo->query("SELECT id_utilizador, nome, email, telefone, data_registo FROM utilizadores WHERE role = 'confeiteiro' ORDER BY data_registo DESC");
    $confeiteiros = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $total_confeiteiros = 0;
    $confeiteiros = [];
    $error_message = "Erro ao buscar confeiteiros: " . $e->getMessage();
}

// Processar ações (adicionar, editar, remover)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $nome = $_POST['nome'];
                $email = $_POST['email'];
                $telefone = $_POST['telefone'];
                $senha = password_hash($_POST['senha'], PASSWORD_DEFAULT);
                
                try {
                    $stmt = $pdo->prepare("INSERT INTO utilizadores (nome, email, telefone, senha, role) VALUES (?, ?, ?, ?, 'confeiteiro')");
                    $stmt->execute([$nome, $email, $telefone, $senha]);
                    $success_message = "Confeiteiro adicionado com sucesso!";
                } catch (PDOException $e) {
                    $error_message = "Erro ao adicionar confeiteiro: " . $e->getMessage();
                }
                break;
                
            case 'delete':
                $id = $_POST['id'];
                try {
                    $stmt = $pdo->prepare("DELETE FROM utilizadores WHERE id_utilizador = ? AND role = 'confeiteiro'");
                    $stmt->execute([$id]);
                    $success_message = "Confeiteiro removido com sucesso!";
                } catch (PDOException $e) {
                    $error_message = "Erro ao remover confeiteiro: " . $e->getMessage();
                }
                break;
        }
        
        // Recarregar a página para atualizar os dados
        header("Location: confeiteiros.php");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Confeiteiros - Cake Garden</title>
    <link rel="stylesheet" href="../../css/admin.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .confeiteiros-container {
            padding: 20px;
        }
        
        .add-confeiteiro-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: #93622B;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary:hover {
            background-color: #7A5023;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .confeiteiros-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .table tr:hover {
            background-color: #f5f5f5;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-card h3 {
            color: #93622B;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .stats-card p {
            color: #666;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h2>Cake Garden</h2>
                <p>Painel Admin</p>
            </div>
            <ul class="menu">
                <li><a href="dashboard.php"><i class="fa-solid fa-gauge"></i> Dashboard</a></li>
                <li><a href="bolos.php"><i class="fa-solid fa-cake-candles"></i> Elementos Personalização</a></li>
                <li><a href="pedidos.php"><i class="fa-solid fa-shopping-cart"></i> Pedidos</a></li>
                <li><a href="clientes.php"><i class="fa-solid fa-users"></i> Clientes</a></li>
                <li class="active"><a href="confeiteiros.php"><i class="fa-solid fa-chef-hat"></i> Confeiteiros</a></li>
                <li><a href="../index.php"><i class="fa-solid fa-home"></i> Ver Site</a></li>
                <li><a href="../logout.php"><i class="fa-solid fa-sign-out-alt"></i> Sair</a></li>
            </ul>
        </div>
        
        <!-- Conteúdo principal -->
        <div class="main-content">
            <div class="header">
                <h1>Gestão de Confeiteiros</h1>
                <div class="user-info">
                    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                    <a href="../logout.php" class="logout-btn"><i class="fa-solid fa-sign-out-alt"></i></a>
                </div>
            </div>
            
            <div class="confeiteiros-container">
                <!-- Estatísticas -->
                <div class="stats-card">
                    <h3><?php echo $total_confeiteiros; ?></h3>
                    <p>Total de Confeiteiros Cadastrados</p>
                </div>
                
                <!-- Mensagens de sucesso/erro -->
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>
                
                <!-- Formulário para adicionar confeiteiro -->
                <div class="add-confeiteiro-form">
                    <h3>Adicionar Novo Confeiteiro</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="form-group">
                            <label for="nome">Nome Completo:</label>
                            <input type="text" id="nome" name="nome" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email:</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="telefone">Telefone:</label>
                            <input type="tel" id="telefone" name="telefone" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="senha">Senha:</label>
                            <input type="password" id="senha" name="senha" required>
                        </div>
                        
                        <button type="submit" class="btn-primary">
                            <i class="fa-solid fa-plus"></i> Adicionar Confeiteiro
                        </button>
                    </form>
                </div>
                
                <!-- Lista de confeiteiros -->
                <div class="confeiteiros-table">
                    <h3 style="padding: 20px; margin: 0; background-color: #f8f9fa; border-bottom: 1px solid #ddd;">
                        Lista de Confeiteiros
                    </h3>
                    
                    <?php if (empty($confeiteiros)): ?>
                        <div style="padding: 20px; text-align: center; color: #666;">
                            Nenhum confeiteiro cadastrado ainda.
                        </div>
                    <?php else: ?>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Email</th>
                                    <th>Telefone</th>
                                    <th>Data de Registro</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($confeiteiros as $confeiteiro): ?>
                                    <tr>
                                        <td><?php echo $confeiteiro['id_utilizador']; ?></td>
                                        <td><?php echo htmlspecialchars($confeiteiro['nome']); ?></td>
                                        <td><?php echo htmlspecialchars($confeiteiro['email']); ?></td>
                                        <td><?php echo htmlspecialchars($confeiteiro['telefone']); ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($confeiteiro['data_registo'])); ?></td>
                                        <td>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Tem certeza que deseja remover este confeiteiro?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $confeiteiro['id_utilizador']; ?>">
                                                <button type="submit" class="btn-danger">
                                                    <i class="fa-solid fa-trash"></i> Remover
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
