<?php
/**
 * Script para limpar sessões corrompidas
 * Execute este arquivo uma vez para resolver problemas de sessão
 */

// Iniciar nova sessão
if (session_status() == PHP_SESSION_ACTIVE) {
    session_destroy();
}

// Limpar cookies de sessão
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Limpar diretório de sessões temporárias (se possível)
$session_path = session_save_path();
if (empty($session_path)) {
    $session_path = sys_get_temp_dir();
}

echo "<h1>Limpeza de Sessões - Cake Garden</h1>";
echo "<p>Sessões corrompidas foram limpas com sucesso!</p>";
echo "<p>Agora pode navegar no site normalmente.</p>";
echo "<p><a href='index.php' style='background: #93622B; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Voltar ao Site</a></p>";

// Auto-remoção do script após execução (opcional)
// unlink(__FILE__);
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 600px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
}

h1 {
    color: #93622B;
    text-align: center;
}

p {
    text-align: center;
    margin: 20px 0;
}
</style>
