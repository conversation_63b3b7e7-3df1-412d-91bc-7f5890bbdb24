/* Estilos para o painel de administração */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: #93622B;
    color: white;
    padding: 20px 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.sidebar .logo {
    text-align: center;
    padding: 0 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 20px;
}

.sidebar .logo h2 {
    font-size: 24px;
    margin-bottom: 5px;
}

.sidebar .logo p {
    font-size: 14px;
    opacity: 0.8;
}

.sidebar .menu {
    list-style: none;
}

.sidebar .menu li {
    margin-bottom: 5px;
}

.sidebar .menu li a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar .menu li a:hover,
.sidebar .menu li.active a {
    background-color: rgba(255,255,255,0.1);
    border-right: 3px solid #fff;
}

.sidebar .menu li a i {
    margin-right: 10px;
    width: 20px;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.header h1 {
    color: #93622B;
    font-size: 28px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info span {
    color: #666;
    font-weight: 500;
}

.logout-btn {
    background-color: #dc3545;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    text-decoration: none;
    transition: background-color 0.3s;
}

.logout-btn:hover {
    background-color: #c82333;
}

/* Stats Container */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    background-color: #93622B;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.stat-info h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 5px;
}

.stat-info p {
    color: #93622B;
    font-size: 28px;
    font-weight: bold;
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-actions h2 {
    color: #93622B;
    margin-bottom: 20px;
    font-size: 22px;
}

.actions-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-card:hover {
    background: #93622B;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.action-card i {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.action-card span {
    font-weight: 500;
    font-size: 16px;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .main-content {
        margin-left: 0;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .actions-container {
        grid-template-columns: 1fr;
    }
}