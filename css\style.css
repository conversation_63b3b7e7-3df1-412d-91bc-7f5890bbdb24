*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;

}

a{
   font-family: 'Playfair Display';


}

body{
    background:#D7C5B2;
    height: 1000px;
}

.menu-bar {
    background:#BBA182;
    text-align: center;
    list-style: none;
}

.menu-bar ul {
    display: inline-flex;
    color: #1a282e;
    list-style: none;
}

.menu-bar ul li {
    width: 120px;
    margin: 15px;
    padding: 0;
    position: relative;
}

.menu-bar ul li a {
    text-decoration: none;
    color: #1a282e;
    gap: 5px;
    display: inline-block;
    margin: 0 1rem;
    transition: all 0.3s ease;
}
.menu-bar ul li a:hover{

border-top:0.2rem solid #925b39;
border-bottom:0.2rem solid #925b39;
padding-top:0.5rem;
padding-bottom:0.4rem;
}




 /* barra de pesquisa */
.search-container {
    display: flex;
    align-items: center;
    background: #ffffff;
    border-radius: 20px;
    padding: 2px 12px;
    width: 399px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    border: 1px solid #d4c1a5;
    /* Removido o transform e outras animações */
}

/* Removido o efeito hover que causava movimento */
.search-container:hover,
.search-container:focus-within {
    box-shadow: 0 3px 6px rgba(139, 74, 34, 0.15);
    border-color: #BBA182;
}

.search-input {
    border: none;
    background: none;
    outline: none;
    padding: 4px 17px;      /* Reduzido  */
    width: 100%;   /* limite da lupa  */
    font-size: 13px;       /* Fonte menor */
    color: #1a282e;
    font-family: 'Playfair Display', serif;
    height: 30px;
}

.search-input::placeholder {
    color: #BBA182;
    opacity: 0.6;
    font-style: italic;
}

.search-button {
    background: none;
    border: none;
    color: #BBA182;
    cursor: pointer;
    padding: 2px;
    transition: all 0.3s ease;
    display: flex;         /* Para centralizar o ícone */
    align-items: center;
}

.search-button:hover {
    color: #8b4a22;
    transform: scale(1.1); /* Efeito sutil de zoom no hover */
}

/* Ajuste do ícone de lupa */
.fa-search {
    font-size: 14px;      /* Ícone menor */
}

.barra {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 180px;       /* Aumentado para acomodar a logo maior */
    padding: 0 20px;
    background: #D7C5B2;  /* Mantendo a cor de fundo */
    position: relative;  /* Adicionado para referência de posicionamento */
}

.logo {
    width: 700px;
    display: flex;
    align-items: center;
    padding-left: 40px;
}

.logo img {
    width: 500px;         /* Aumentado significativamente */
    height: 500px;        /* Aumentado significativamente */
    object-fit: contain;
    padding: 0;
    margin: 0;

}

.nav-list-icon{
    display: flex;
    margin: 45px;
    gap: 100px;

}


.side-image {
    position: absolute; /* Posiciona a imagem de forma absoluta */
    top: 40%; /* Subiu a imagem para ficar mais alta */
    right: 50px; /* Ajusta a distância da direita */
    transform: translateY(-50%); /* Centraliza perfeitamente no eixo vertical */
    width: 450px; /* Aumenta a largura da imagem */
    height: auto; /* Mantém a proporção da imagem */
    max-height: 600px; /* Limita a altura máxima */
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3); /* Adiciona uma sombra elegante */
    border-radius: 0; /* Remove as bordas arredondadas */
    overflow: hidden; /* Garante que a imagem não ultrapasse os limites */
    z-index: 10; /* Garante que a imagem fique acima de outros elementos */
    background: none; /* Remove o fundo branco */
    padding: 0; /* Remove o espaçamento interno */
}

.side-image img {
    width: 100%; /* Garante que a imagem ocupe todo o contêiner */
    height: auto; /* Mantém a proporção */
    object-fit: cover; /* Ajusta a imagem para cobrir o espaço */
    border-radius: 0; /* Remove bordas arredondadas internas */
}
.fa-cart-plus{
    color:#858686;
    font-size: 30px;

}

.fa-cart-plus:hover{
    color: #1a282e;
    font-size: 35px;

}

.fa-sign-out-alt{
    color: #858686;
    font-size: 30px;

}

.fa-user{
    color: #858686;
    font-size: 30px;
}

.fa-user:hover{
    color: #1a282e;
    font-size: 35px;
}

/* Botão Admin */
.admin-btn {
    color: #858686;
    font-size: 30px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.admin-btn:hover {
    color: #93622B;
    font-size: 35px;
    transform: rotate(90deg);
}

.admin-btn .fas {
    transition: all 0.3s ease;
}

/* Botão Dashboard usa os estilos padrão da navbar - sem estilos especiais */

.nav-list{

    font-family: 'Playfair Display';
    display: flex;
    list-style: none;

    margin-top: 50px;
    padding-right: 150px;
    text-align: center;


}

.nav-list-icon{
    width: 300px;
    display: flex;
    gap: 32px;
    justify-content: flex-end;
    margin-right: 40px;
}

.nav-list a:hover{
    color: #8b4a22;



}


.nav{
    display: flex;
    justify-content: space-between;
    max-width:1280px ;
    margin-inline:auto ;
    padding-block: 16px;
    border-radius: 8px;


}



h2{
    font-family: 'Cormorant Garamond', serif;
    font-weight: bold;
    color:#8b4a22;
    font-size: 3rem;
    text-align: left;



}
.h1{
    text-align: center;
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    color: #93622B;
    text-align: left;

}


.cake-img {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 50px;
    padding: 0 20px;
    flex-wrap: wrap;
}

.cake-in {
    width: 300px;
    height: 300px;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}



.cake-in img {
    width: 100%;
    height: 100%;
    object-fit: contain, fill;
    transition: transform 0.3s ease;
    padding: 0;
    margin: 0;
}
.card-img {
  width: 100%;
  height: 200px; /* ou ajuste conforme necessário */
  object-fit: cover;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}


/* .teste {
    object-fit: fill;
} */

.cake-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: all 0.3s ease;
    padding: 20px;
    text-align: center;
}

.cake-overlay i {
    font-size: 2.5rem;
    color: white;
    margin-bottom: 15px;
}

.cake-overlay p {
    color: white;
    font-size: 1.2rem;
    font-family: 'Playfair Display', serif;
}

.cake-in:hover .cake-overlay {
    opacity: 1;
}

.cake-in:hover img {
    transform: scale(1.1);
}

.home-cake {
    position: relative;
    width: 100%;
    min-height: 90vh;
    padding-top: 120px;
    display: flex;
    justify-content: space-between;
}

.cake-text {
    width: 40%;
    padding-left: 100px;
    position: relative;
    z-index: 2;
}

.img-cake {
    width: 450px;
    height: 450px;
    right: 400px;
    top: 180px;


}

.btn {
    display: inline-block;
    padding: 15px 30px;
    background: linear-gradient(45deg, #93622B, #BBA182);
    color: white;
    text-decoration: none;
    border-radius: 30px;
    font-size: 18px;
    font-family: 'Playfair Display', serif;
    border: none;
    box-shadow: 0 4px 15px rgba(147, 98, 43, 0.3);
    transition: all 0.3s ease;
    margin-top: 30px;
}

.btn:hover {
    background: linear-gradient(45deg, #BBA182, #93622B);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(147, 98, 43, 0.4);
}

/* Ajustes responsivos */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding-top: 100px;
    }

    .hero-content {
        width: 100%;
        padding: 0 20px;
    }

    .hero-image {
        position: relative;
        width: 90%;
        right: auto;
        margin-top: 50px;
    }
}

/* Hero Section */
.hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    padding: 0 5%;
    background: #D7C5B2;
    position: relative;
    overflow: hidden;
    margin-top: 20px;
}

.hero-content {
    width: 50%;
    z-index: 2;
    padding: 20px;
}

.hero-image {
    width: 45%;
    margin-left: 5%;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

/* Animações */
@keyframes slideDown {
    from { transform: translateY(-100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(100px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

/* Classes de animação */
.animate-slide-down {
    animation: slideDown 1s ease forwards;
}

.animate-slide-up {
    animation: slideUp 1s ease forwards;
}

.animate-fade-in {
    animation: fadeIn 1.5s ease forwards;
}

.animate-fade-in-delay {
    opacity: 0;
    animation: fadeIn 1.5s ease forwards;
    animation-delay: 0.5s;
}

.animate-slide-in {
    animation: slideIn 1.2s ease forwards;
}

.animate-bounce {
    display: inline-block;
    animation: bounce 2s infinite;
}

/* Ajustes responsivos */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding-top: 50px;
    }

    .hero-content {
        width: 100%;
        padding: 0 20px;
    }

    .hero-image {
        width: 90%;
        margin: 30px auto;
    }
}

/* Ajuste do botão existente para o hero */
.hero .btn {
    display: inline-block;
    padding: 15px 30px;
    background: #93622B;
    color: white;
    text-decoration: none;
    border-radius: 30px;
    font-size: 18px;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.hero .btn:hover {
    background: #BBA182;
    transform: translateY(-3px);
}

/* Rodapé */
footer {
    padding: 0px 0px;
    width: 100%;
    background-color: #BBA182; /* Cor de fundo */
}

.footer_content {
    margin-top: 80px;
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* Três colunas */
    gap: 2rem;
    padding: 3rem 3.5rem;
}

h3 {
    font-family: 'Playfair Display', serif;
    font-size: 28px;
    color: #93622B;
    text-align: center;
    margin-bottom: 15px;
}

/* Estilo genérico para todas as seções do footer */
.footer_section {
    text-align: center; /* Centraliza o conteúdo */
}
.footer_section h3 {
    font-size: 1.2rem; /* Tamanho do título */
    margin-bottom: 1rem; /* Espaçamento inferior */
    color: #93622B; /* Cor do texto */
}

.footer_section ul {
    list-style: none; /* Remove os marcadores da lista */
    padding: 0; /* Remove o padding */
    margin: 0; /* Remove a margem */
}

.footer_section ul li {
    margin-bottom: 0.5rem; /* Espaçamento entre os itens */
}

.footer_section ul li a {
    color: #93622B; /* Cor dos links */
    text-decoration: none; /* Remove o sublinhado */
    font-size: 1rem; /* Tamanho do texto */
    transition: color 0.3s ease; /* Animação ao passar o mouse */
}

.footer_section ul li a:hover {
    color: #8b4a22; /* Cor ao passar o mouse */
}
/* Ajuste dos ícones sociais */
.footer_social ul {
    display: flex; /* Alinha os ícones horizontalmente */
    justify-content: center; /* Centraliza os ícones */
    gap: 1rem; /* Espaçamento entre os ícones */
    list-style: none; /* Remove os marcadores */
    padding: 0;
    margin: 0;
}

.footer_social ul li a {
    font-size: 1.5rem; /* Tamanho dos ícones */
    color: #93622B; /* Cor dos ícones */
    transition: color 0.3s ease;
}

.footer_social ul li a:hover {
    color: #8b4a22; /* Cor ao passar o mouse */
}

/* Copyright */
.footer_copyright {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.9rem;
    color: #93622B;
}

/* About us */

.about-us {
    padding: 50px 0;
    text-align: center;
    margin-bottom: 20px;

}

h1 {
    font-family:  'Dancing Script';
    font-size: 28px;
    color: #93622B;
    margin-bottom: 20px;
}

span {
    color: #5d2f13;
    font-weight: bold;

}

.decorative-line {
    display: flex; /* Alinha os elementos em linha */
    align-items: center; /* Centraliza verticalmente */
    justify-content: center; /* Centraliza horizontalmente */
    gap: 10px; /* Espaçamento entre os elementos */
    margin: 20px 0; /* Espaçamento superior e inferior */
}

.decorative-line .line {
    flex: 1; /* Faz as linhas ocuparem o espaço disponível */
    height: 4px; /* Altura da linha */
    background-color: #93622B; /* Cor dourada */
}

.decorative-line span {
    font-size: 1rem; /* Tamanho do texto "Veja Abaixo" */
    color: #8b4a22; /* Cor dourada */
    font-family: 'Cormorant Garamond', serif; /* Fonte elegante */
    text-transform: uppercase; /* Texto em maiúsculas */
    letter-spacing: 2px; /* Espaçamento entre letras */
}

.about-us-text  {
    font-family: 'Cormorant Garamond', serif;;
    font-size: 18px;
    color: #5d2f13;
    line-height: 1.6;
    margin-bottom: 20px;
    text-align: justify;
    margin-left: 10px;

}

.about-us .fa-bowl-food {
    display: block; /* Faz o ícone ocupar uma linha inteira */
    font-size: 3rem; /* Aumenta o tamanho do ícone */
    color: #93622B; /* Cor dourada */
    margin: 20px auto 10px; /* Espaçamento superior e inferior, centralizado horizontalmente */
    text-align: left; /* Alinha o ícone à esquerda */
    margin-left: 10%; /* Move o ícone mais para o lado esquerdo */
    font-weight: bold;
}

.about-us .fa-birthday-cake{
    display: block; /* Faz o ícone ocupar uma linha inteira */
    font-size: 3rem; /* Aumenta o tamanho do ícone */
    color: #93622B; /* Cor dourada */
    margin: 20px auto 10px; /* Espaçamento superior e inferior, centralizado horizontalmente */
    text-align: left; /* Alinha o ícone à esquerda */
    margin-left: 10%; /* Move o ícone mais para o lado esquerdo */
    font-weight: bold;

}

.about-us .fa-camera{
    display: block; /* Faz o ícone ocupar uma linha inteira */
    font-size: 3rem; /* Aumenta o tamanho do ícone */
    color: #93622B; /* Cor dourada */
    margin: 20px auto 10px; /* Espaçamento superior e inferior, centralizado horizontalmente */
    text-align: left; /* Alinha o ícone à esquerda */
    margin-left: 10%; /* Move o ícone mais para o lado esquerdo */
    font-weight: bold;

}

.about-us .fa-heart{
    display: block; /* Faz o ícone ocupar uma linha inteira */
    font-size: 3rem; /* Aumenta o tamanho do ícone */
    color: #93622B; /* Cor dourada */
    margin: 20px auto 10px; /* Espaçamento superior e inferior, centralizado horizontalmente */
    text-align: left; /* Alinha o ícone à esquerda */
    margin-left: 10%; /* Move o ícone mais para o lado esquerdo */
    font-weight: bold;

}

.icons_about {
    display: flex;
    margin-top: 20px;
    margin-right: 10%;


}

.about-us-text{
    font-weight: bold;
    font-size: 18px;

}

/*formulário de contacto - Estilo principal da seção*/
.contacto {
    padding: 70px 20px; /* Aumentado o padding vertical para dar mais espaço */
}

/* Container que organiza as três colunas lado a lado */
.contact-container {
    display: flex; /* Organiza os elementos em linha */
    justify-content: center; /* Centraliza horizontalmente */
    flex-wrap: wrap; /* Permite quebra de linha em telas menores */
    max-width: 1300px; /* Aumentado para dar mais espaço */
    margin: 0 auto; /* Centraliza o container na página */
    gap: 40px; /* Aumentado o espaçamento entre as colunas */
}

/* Estilo de cada coluna individual */
.contact-column {
    flex: 1; /* Faz com que cada coluna ocupe espaço igual */
    min-width: 280px; /* Aumentado o tamanho mínimo */
    max-width: 380px; /* Aumentado o tamanho máximo */
    padding: 25px; /* Aumentado o padding interno */
    text-align: center; /* Centraliza o texto */
    background-color: rgba(255, 255, 255, 0.1); /* Fundo sutil */
    border-radius: 10px; /* Bordas arredondadas */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05); /* Sombra suave */
}

/* Estilo dos títulos de cada coluna */
.contact-column h3 {
    font-family: 'Cormorant Garamond', serif;
    color: #93622B;
    margin-bottom: 25px; /* Aumentado o espaço abaixo do título */
    font-size: 28px; /* Aumentado o tamanho da fonte */
    position: relative;
    font-weight: bold; /* Deixando em negrito */
}

/* Linha decorativa abaixo dos títulos */
.contact-column h3:after {
    content: ''; /* Elemento vazio */
    display: block;
    width: 100px; /* Aumentado o comprimento da linha */
    height: 3px; /* Aumentado a espessura da linha */
    background: #93622B;
    margin: 12px auto; /* Centralizado com mais espaço */
}

/* Estilo dos parágrafos em cada coluna */
.contact-column p {
    margin-bottom: 15px; /* Mais espaço entre parágrafos */
    color: #5d2f13;
    font-size: 18px; /* Aumentado o tamanho da fonte */
    line-height: 1.6; /* Melhor espaçamento entre linhas */
}

/* Estilo do formulário */
.form {
    display: flex;
    flex-direction: column; /* Organiza os campos em coluna */
}

/* Estilo dos campos de texto */
.field {
    border-radius: 8px;
    margin-bottom: 20px; /* Mais espaço entre os campos */
    height: 50px; /* Aumentado a altura dos campos */
    background: #BBA182;
    border: none;
    padding: 0 20px; /* Mais padding horizontal */
    color: #5d2f13;
    font-size: 16px; /* Aumentado o tamanho da fonte */
    font-family: 'Playfair Display', serif; /* Mantendo a fonte consistente */
}


/* Estilo da área de texto para mensagem */
.message {
    height: 150px; /* Aumentado a altura da área de texto */
    border-radius: 8px;
    background: #BBA182;
    border: none;
    padding: 20px; /* Mais padding em todos os lados */
    margin-bottom: 20px; /* Mais espaço abaixo */
    color: #5d2f13;
    resize: none; /* Impede o redimensionamento pelo usuário */
    font-size: 16px; /* Aumentado o tamanho da fonte */
    font-family: 'Playfair Display', serif; /* Mantendo a fonte consistente */
}

/* Container do botão para centralização */
.button-container {
    display: flex;
    justify-content: center;
}

/* Estilo do botão de envio */
button {
    border: none;
    padding: 15px 30px; /* Aumentado o padding para um botão maior */
    font-size: 18px; /* Aumentado o tamanho da fonte */
    border-radius: 8px; /* Bordas mais arredondadas */
    cursor: pointer;
    transition: 0.3s ease; /* Transição suave ao passar o mouse */
    background: #93622B;
    color: white;
    font-weight: bold; /* Texto em negrito */
    letter-spacing: 1px; /* Espaçamento entre letras */
}

/* Efeito hover do botão */
button:hover {
    background: #BBA182;
    transform: translateY(-3px); /* Efeito de elevação ao passar o mouse */
    box-shadow: 0 5px 15px rgba(147, 98, 43, 0.3); /* Sombra ao passar o mouse */
}

/* Estilo para ícones nos contatos */
.contact-column i {
    margin-right: 10px; /* Espaço à direita dos ícones */
    color: #93622B; /* Cor dos ícones */
    font-size: 20px; /* Tamanho dos ícones */
}

/* Estilo para o aviso de encomendas */
.aviso-encomenda {
    margin-top: 25px;
    padding: 15px;
    background-color: #FFF8F0;
    border-left: 4px solid #93622B;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.aviso-encomenda i {
    color: #93622B;
    font-size: 24px;
    margin-bottom: 10px;
}

.aviso-encomenda h4 {
    color: #93622B;
    font-family: 'Lobster Two', cursive;
    font-size: 18px;
    margin-bottom: 8px;
}

.aviso-encomenda p {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

/*fim de contacto*/


.user-initial {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: #b5b6b6;
    color: white;
    font-weight: bold;
    margin-right: 10px;
    cursor: pointer;
}

.user-initial span {
    font-size: 18px;
}

.user-initial:hover {
    background-color: #2c3e50;
}

/* Estilos responsivos gerais */
@media (max-width: 1200px) {
    .cake-img {
        gap: 15px;
    }

    .footer_content {
        grid-template-columns: repeat(3, 1fr);
        padding: 2rem;
    }
}

@media (max-width: 992px) {
    .barra {
        height: auto;
        flex-direction: column;
        padding: 20px;
    }

    .logo {
        width: 100%;
        padding-left: 0;
        justify-content: center;
        margin-bottom: 20px;
    }

    .logo img {
        width: 300px;
        height: 300px;
    }

    .search-container {
        margin: 15px 0;
    }

    .nav-list-icon {
        margin: 15px 0;
        justify-content: center;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 30px 20px;
    }

    .hero-content, .hero-image {
        width: 100%;
        margin: 0;
    }

    .hero-image {
        margin-top: 30px;
    }

    .cake-img {
        justify-content: center;
    }

    .cake-in {
        width: 45%;
    }
}

@media (max-width: 768px) {
    .menu-bar ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    .menu-bar ul li {
        width: auto;
        margin: 10px;
    }

    .cake-img {
        flex-direction: column;
        align-items: center;
    }

    .cake-in {
        width: 90%;
        max-width: 400px;
        margin-bottom: 20px;
    }

    .footer_content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer_section {
        text-align: center;
    }

    .h1 {
        font-size: 3rem;
    }

    h2 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .search-container {
        width: 90%;
    }

    .menu-bar ul li {
        width: 100%;
        margin: 5px 0;
    }

    .menu-bar ul li a {
        display: block;
        padding: 10px;
    }

    .h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 16px;
    }
}

/* Responsividade para o formulário de contato */
@media (max-width: 992px) {
    .contact-container {
        gap: 30px; /* Reduz o espaçamento em telas médias */
    }
}

@media (max-width: 768px) {
    .contact-container {
        flex-direction: column; /* Empilha as colunas */
        align-items: center; /* Centraliza as colunas */
    }

    .contact-column {
        width: 100%;
        max-width: 500px; /* Aumenta a largura máxima em telas pequenas */
        margin-bottom: 30px; /* Espaço entre as colunas empilhadas */
    }
}

/* Formulário de contacto responsivo */
@media (max-width: 768px) {
    .contact-container {
        flex-direction: column;
        align-items: center;
    }

    .contact-column {
        width: 90%;
        max-width: none;
        margin-bottom: 20px;
    }

    .field, .message {
        width: 100%;
    }
}

/* Menu móvel */
.mobile-menu-toggle {
    display: none;
    font-size: 24px;
    color: #93622B;
    cursor: pointer;
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .menu-bar {
        position: relative;
    }

    .menu-bar ul {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: #BBA182;
        z-index: 999;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }

    .menu-bar ul.show {
        display: flex;
    }

    .menu-bar ul li {
        width: 100% !important;
        margin: 0;
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .menu-bar ul li a {
        padding: 15px;
        width: 100%;
        display: block;
        text-align: left;
    }
}

/* Imagens responsivas */
img {
    max-width: 100%;
    height: auto;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .hero-image img {
        max-width: 90%;
        margin: 0 auto;
        display: block;
    }
}

/* Galeria responsiva */
@media (max-width: 992px) {
    .grid-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 576px) {
    .grid-container {
        grid-template-columns: 1fr;
    }
}

/* Blog responsivo */
@media (max-width: 768px) {
    .blog-container {
        padding: 20px;
    }

    .blog-post {
        flex-direction: column;
    }

    .blog-image, .blog-content {
        width: 100%;
    }

    .blog-image {
        margin-bottom: 20px;
    }
}

/* Página de clientes responsiva */
@media (max-width: 768px) {
    .depoimentos-grid {
        grid-template-columns: 1fr;
    }

    .depoimento-card {
        margin-bottom: 20px;
    }
}

/* Estilos para Política de Privacidade */
.privacy-policy {
    max-width: 1000px;
    margin: 50px auto;
    padding: 20px;
    font-family: 'Playfair Display', serif;
}

.privacy-policy h1 {
    color: #93622B;
    text-align: center;
    margin-bottom: 30px;
}

.policy-section {
    margin-bottom: 30px;
}

.policy-section h2 {
    color: #93622B;
    font-size: 1.8rem;
    margin-bottom: 15px;
}

.policy-section p, .policy-section ul {
    line-height: 1.6;
    color: #333;
}

.policy-section ul {
    margin-left: 20px;
}

/* Banner de Consentimento de Cookies */
.cookie-banner {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(215, 197, 178, 0.95);
    padding: 15px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.cookie-content p {
    flex: 1;
    margin-right: 20px;
    font-family: 'Playfair Display', serif;
}

.cookie-buttons {
    display: flex;
    gap: 10px;
}

.btn-cookie {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Playfair Display', serif;
    transition: all 0.3s ease;
}

.btn-cookie.accept {
    background: #93622B;
    color: white;
}

.btn-cookie.decline {
    background: #858686;
    color: white;
}

.btn-cookie.customize {
    background: transparent;
    border: 1px solid #93622B;
    color: #93622B;
}

.btn-cookie:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}