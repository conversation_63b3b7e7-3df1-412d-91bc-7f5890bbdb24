<?php
include '../db.php';
// Removemos o session_start() daqui pois já é iniciado em db.php -> includes/init.php

// Verifica se o usuário está logado e é admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../login.php");
    exit;
}

// Busca informações das tabelas para personalização
try {
    // Contar elementos de cada tabela
    $stats = [];
    $stats['tipo_bolo'] = $pdo->query("SELECT COUNT(*) as total FROM tipo_bolo")->fetch()['total'];
    $stats['massas'] = $pdo->query("SELECT COUNT(*) as total FROM massas")->fetch()['total'];
    $stats['coberturas'] = $pdo->query("SELECT COUNT(*) as total FROM coberturas")->fetch()['total'];
    $stats['recheios'] = $pdo->query("SELECT COUNT(*) as total FROM recheios")->fetch()['total'];
    $stats['pesos'] = $pdo->query("SELECT COUNT(*) as total FROM pesos")->fetch()['total'];
    $stats['formatos'] = $pdo->query("SELECT COUNT(*) as total FROM formatos")->fetch()['total'];
    $stats['decoracao'] = $pdo->query("SELECT COUNT(*) as total FROM decoracao")->fetch()['total'];
    $stats['adicionais'] = $pdo->query("SELECT COUNT(*) as total FROM adicionais")->fetch()['total'];
} catch (PDOException $e) {
    $stats = array_fill_keys(['tipo_bolo', 'massas', 'coberturas', 'recheios', 'pesos', 'formatos', 'decoracao', 'adicionais'], 0);
}

// Processar ações para diferentes tabelas
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $tabela = $_POST['tabela'] ?? '';
        $action = $_POST['action'];

        try {
            switch ($action) {
                case 'add':
                    switch ($tabela) {
                        case 'tipo_bolo':
                            $nome = $_POST['nome'];
                            $preco = $_POST['preco'];
                            $stmt = $pdo->prepare("INSERT INTO tipo_bolo (nome_tipo, preco) VALUES (?, ?)");
                            $stmt->execute([$nome, $preco]);
                            $success_message = "Tipo de bolo adicionado com sucesso!";
                            break;

                        case 'massas':
                            $nome = $_POST['nome'];
                            $stmt = $pdo->prepare("INSERT INTO massas (nome) VALUES (?)");
                            $stmt->execute([$nome]);
                            $success_message = "Massa adicionada com sucesso!";
                            break;

                        case 'coberturas':
                            $nome = $_POST['nome'];
                            $stmt = $pdo->prepare("INSERT INTO coberturas (nome) VALUES (?)");
                            $stmt->execute([$nome]);
                            $success_message = "Cobertura adicionada com sucesso!";
                            break;

                        case 'recheios':
                            $nome = $_POST['nome'];
                            $stmt = $pdo->prepare("INSERT INTO recheios (nome) VALUES (?)");
                            $stmt->execute([$nome]);
                            $success_message = "Recheio adicionado com sucesso!";
                            break;

                        case 'pesos':
                            $peso = $_POST['peso'];
                            $preco = $_POST['preco'];
                            $stmt = $pdo->prepare("INSERT INTO pesos (peso, preco) VALUES (?, ?)");
                            $stmt->execute([$peso, $preco]);
                            $success_message = "Peso adicionado com sucesso!";
                            break;

                        case 'formatos':
                            $nome = $_POST['nome'];
                            $stmt = $pdo->prepare("INSERT INTO formatos (nome) VALUES (?)");
                            $stmt->execute([$nome]);
                            $success_message = "Formato adicionado com sucesso!";
                            break;

                        case 'decoracao':
                            $nome = $_POST['nome'];
                            $preco = $_POST['preco'];
                            $stmt = $pdo->prepare("INSERT INTO decoracao (nome_decoracao, preco) VALUES (?, ?)");
                            $stmt->execute([$nome, $preco]);
                            $success_message = "Decoração adicionada com sucesso!";
                            break;

                        case 'adicionais':
                            $nome = $_POST['nome'];
                            $preco = $_POST['preco'];
                            $stmt = $pdo->prepare("INSERT INTO adicionais (nome, preco) VALUES (?, ?)");
                            $stmt->execute([$nome, $preco]);
                            $success_message = "Adicional adicionado com sucesso!";
                            break;
                    }
                    break;

                case 'delete':
                    $id = $_POST['id'];
                    switch ($tabela) {
                        case 'tipo_bolo':
                            $stmt = $pdo->prepare("DELETE FROM tipo_bolo WHERE id_tipo = ?");
                            $stmt->execute([$id]);
                            $success_message = "Tipo de bolo removido com sucesso!";
                            break;

                        case 'massas':
                            $stmt = $pdo->prepare("DELETE FROM massas WHERE id_massa = ?");
                            $stmt->execute([$id]);
                            $success_message = "Massa removida com sucesso!";
                            break;

                        case 'coberturas':
                            $stmt = $pdo->prepare("DELETE FROM coberturas WHERE id_cobertura = ?");
                            $stmt->execute([$id]);
                            $success_message = "Cobertura removida com sucesso!";
                            break;

                        case 'recheios':
                            $stmt = $pdo->prepare("DELETE FROM recheios WHERE id_recheio = ?");
                            $stmt->execute([$id]);
                            $success_message = "Recheio removido com sucesso!";
                            break;

                        case 'pesos':
                            $stmt = $pdo->prepare("DELETE FROM pesos WHERE id_peso = ?");
                            $stmt->execute([$id]);
                            $success_message = "Peso removido com sucesso!";
                            break;

                        case 'formatos':
                            $stmt = $pdo->prepare("DELETE FROM formatos WHERE id_formato = ?");
                            $stmt->execute([$id]);
                            $success_message = "Formato removido com sucesso!";
                            break;

                        case 'decoracao':
                            $stmt = $pdo->prepare("DELETE FROM decoracao WHERE id_decoracao = ?");
                            $stmt->execute([$id]);
                            $success_message = "Decoração removida com sucesso!";
                            break;

                        case 'adicionais':
                            $stmt = $pdo->prepare("DELETE FROM adicionais WHERE id_adicional = ?");
                            $stmt->execute([$id]);
                            $success_message = "Adicional removido com sucesso!";
                            break;
                    }
                    break;
            }
        } catch (PDOException $e) {
            $error_message = "Erro: " . $e->getMessage();
        }

        // Recarregar a página para atualizar os dados
        header("Location: bolos.php");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Elementos para Personalização - Cake Garden</title>
    <link rel="stylesheet" href="../../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .bolos-container {
            padding: 20px;
        }
        
        .add-bolo-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .btn-primary {
            background-color: #93622B;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary:hover {
            background-color: #7A5023;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .bolos-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .table tr:hover {
            background-color: #f5f5f5;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-card h3 {
            color: #93622B;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .stats-card p {
            color: #666;
            font-size: 16px;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }

        .tab {
            flex: 1;
            padding: 15px 10px;
            text-align: center;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            border-right: 1px solid #ddd;
        }

        .tab:last-child {
            border-right: none;
        }

        .tab.active {
            background: #93622B;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            background: white;
            padding: 20px;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        .element-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
        }

        .form-row .form-group {
            flex: 1;
        }

        .element-list {
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .element-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .element-item:last-child {
            border-bottom: none;
        }

        .element-item:hover {
            background: #f8f9fa;
        }

        .element-info {
            flex: 1;
        }

        .element-name {
            font-weight: bold;
            color: #333;
        }

        .element-price {
            color: #93622B;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h2>Cake Garden</h2>
                <p>Painel Admin</p>
            </div>
            <ul class="menu">
                <li><a href="dashboard.php"><i class="fa-solid fa-gauge"></i> Dashboard</a></li>
                <li class="active"><a href="bolos.php"><i class="fa-solid fa-cake-candles"></i> Elementos Personalização</a></li>
                <li><a href="pedidos.php"><i class="fa-solid fa-shopping-cart"></i> Pedidos</a></li>
                <li><a href="clientes.php"><i class="fa-solid fa-users"></i> Clientes</a></li>
                <li><a href="confeiteiros.php"><i class="fa-solid fa-chef-hat"></i> Confeiteiros</a></li>
                <li><a href="../index.php"><i class="fa-solid fa-home"></i> Ver Site</a></li>
                <li><a href="../logout.php"><i class="fa-solid fa-sign-out-alt"></i> Sair</a></li>
            </ul>
        </div>
        
        <!-- Conteúdo principal -->
        <div class="main-content">
            <div class="header">
                <h1>Gestão de Elementos para Personalização</h1>
                <div class="user-info">
                    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                    <a href="../logout.php" class="logout-btn"><i class="fa-solid fa-sign-out-alt"></i></a>
                </div>
            </div>
            
            <div class="bolos-container">
                <!-- Mensagens de sucesso/erro -->
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>

                <!-- Estatísticas dos elementos -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div class="stats-card">
                        <h3><?php echo $stats['tipo_bolo']; ?></h3>
                        <p>Tipos de Bolo</p>
                    </div>
                    <div class="stats-card">
                        <h3><?php echo $stats['massas']; ?></h3>
                        <p>Massas</p>
                    </div>
                    <div class="stats-card">
                        <h3><?php echo $stats['coberturas']; ?></h3>
                        <p>Coberturas</p>
                    </div>
                    <div class="stats-card">
                        <h3><?php echo $stats['recheios']; ?></h3>
                        <p>Recheios</p>
                    </div>
                    <div class="stats-card">
                        <h3><?php echo $stats['pesos']; ?></h3>
                        <p>Pesos</p>
                    </div>
                    <div class="stats-card">
                        <h3><?php echo $stats['formatos']; ?></h3>
                        <p>Formatos</p>
                    </div>
                    <div class="stats-card">
                        <h3><?php echo $stats['decoracao']; ?></h3>
                        <p>Decorações</p>
                    </div>
                    <div class="stats-card">
                        <h3><?php echo $stats['adicionais']; ?></h3>
                        <p>Adicionais</p>
                    </div>
                </div>

                <!-- Abas para gerenciar elementos -->
                <div class="tabs">
                    <button class="tab active" onclick="showTab('tipo_bolo')">Tipos de Bolo</button>
                    <button class="tab" onclick="showTab('massas')">Massas</button>
                    <button class="tab" onclick="showTab('coberturas')">Coberturas</button>
                    <button class="tab" onclick="showTab('recheios')">Recheios</button>
                    <button class="tab" onclick="showTab('pesos')">Pesos</button>
                    <button class="tab" onclick="showTab('formatos')">Formatos</button>
                    <button class="tab" onclick="showTab('decoracao')">Decorações</button>
                    <button class="tab" onclick="showTab('adicionais')">Adicionais</button>
                </div>

                <!-- Conteúdo das abas -->
                <div class="tab-content active" id="tipo_bolo">
                    <h3>Gestão de Tipos de Bolo</h3>
                    <p>Esta funcionalidade permite gerir os tipos de bolo disponíveis para personalização.</p>
                    <p>Para adicionar novos elementos, utilize o painel de administração da base de dados.</p>
                </div>

                <div class="tab-content" id="massas">
                    <h3>Gestão de Massas</h3>
                    <p>Esta funcionalidade permite gerir as massas disponíveis para personalização.</p>
                </div>

                <div class="tab-content" id="coberturas">
                    <h3>Gestão de Coberturas</h3>
                    <p>Esta funcionalidade permite gerir as coberturas disponíveis para personalização.</p>
                </div>

                <div class="tab-content" id="recheios">
                    <h3>Gestão de Recheios</h3>
                    <p>Esta funcionalidade permite gerir os recheios disponíveis para personalização.</p>
                </div>

                <div class="tab-content" id="pesos">
                    <h3>Gestão de Pesos</h3>
                    <p>Esta funcionalidade permite gerir os pesos disponíveis para personalização.</p>
                </div>

                <div class="tab-content" id="formatos">
                    <h3>Gestão de Formatos</h3>
                    <p>Esta funcionalidade permite gerir os formatos disponíveis para personalização.</p>
                </div>

                <div class="tab-content" id="decoracao">
                    <h3>Gestão de Decorações</h3>
                    <p>Esta funcionalidade permite gerir as decorações disponíveis para personalização.</p>
                </div>

                <div class="tab-content" id="adicionais">
                    <h3>Gestão de Adicionais</h3>
                    <p>Esta funcionalidade permite gerir os adicionais disponíveis para personalização.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Esconder todas as abas
            var tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(function(content) {
                content.classList.remove('active');
            });

            // Remover classe active de todos os botões
            var tabs = document.querySelectorAll('.tab');
            tabs.forEach(function(tab) {
                tab.classList.remove('active');
            });

            // Mostrar a aba selecionada
            document.getElementById(tabName).classList.add('active');

            // Adicionar classe active ao botão clicado
            event.target.classList.add('active');
        }
    </script>
</body>
</html>

