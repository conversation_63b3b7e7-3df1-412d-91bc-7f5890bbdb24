<?php
// Carregar configurações de segurança
require_once 'includes/init.php';

$host = 'localhost';
$dbname = 'loja_bolos';
$user = 'root';
$pass = 'iefp2025*';

// Criar ligação com PDO
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // Prevenir injeção SQL usando prepared statements
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
    
} catch(PDOException $e) {
    // Log do erro sem expor detalhes sensíveis
    error_log("Erro na conexão com o banco de dados: " . $e->getMessage());
    die("Não foi possível conectar ao banco de dados. Por favor, tente novamente mais tarde.");
}

// Função para criptografar dados sensíveis antes de armazenar
function encryptData($data) {
    return Security::encrypt($data);
}

// Função para descriptografar dados
function decryptData($data) {
    return Security::decrypt($data);
}
