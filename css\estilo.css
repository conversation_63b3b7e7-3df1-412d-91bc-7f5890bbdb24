*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body{
    background-color: #D7C5B2;
    color: #333;
    font-size: 16px;
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;

}

/* Estilos compartilhados para login e registro */
.login, .registo {
    margin: 0px 15px;
}

.form-box {
    width: 100%;
    max-width: 450px;
    background: #d4c1a5;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 40px;
}

.form-box h2 {
    margin-bottom: 20px;
    padding: 20px;
    color: #333;
    font-size: 34px;
    text-align: center;
    font-weight: bold;
}

.form-box input {
    width: 100%;
    padding: 12px;
    background: #fff;
    border: none;
    border-radius: 5px;
    margin-bottom: 20px;
    outline: none;
    font-size: 16px;
    color: #333;
}

.form-box .btn {
    width: 100%;
    background: #8b4a22;
    border: none;
    padding: 12px;
    border-radius: 5px;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    transition: background 0.3s ease;
    font-weight: 500;
}

.form-box .btn:hover {
    background: #7a3b1c;
}

.form-box p {
    margin-top: 20px;
    font-size: 14px;
    color: #333;
    text-align: center;
}

.form-box p a {
    color: black;
    text-decoration: none;
    font-weight: bold;
}

.form-box p a:hover {
    text-decoration: underline;
}

/* Mensagem de erro/sucesso */
.message {
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.8);
    color: #333;
    font-size: 14px;
}
