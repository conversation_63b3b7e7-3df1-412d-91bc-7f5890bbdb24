<?php
require_once 'db.php';
require_once 'includes/header.php';

$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nome = $_POST['nome'] ?? '';
    $email = $_POST['email'] ?? '';
    $direito = $_POST['direito'] ?? '';
    $detalhes = $_POST['detalhes'] ?? '';
    
    try {
        // Registrar solicitação RGPD
        $stmt = $pdo->prepare("INSERT INTO rgpd_solicitacoes (nome, email, tipo_direito, detalhes, data_solicitacao, status) VALUES (?, ?, ?, ?, NOW(), 'pendente')");
        $stmt->execute([$nome, $email, $direito, $detalhes]);
        
        // Enviar email de confirmação
        $to = $email;
        $subject = "Confirmação de Solicitação RGPD - Cake Garden";
        $body = "Olá $nome,\n\nRecebemos sua solicitação relacionada ao direito de $direito. Sua solicitação será processada dentro de 30 dias conforme exigido pelo RGPD.\n\nObrigado,\nEquipe Cake Garden";
        $headers = "From: <EMAIL>\r\n";
        
        mail($to, $subject, $body, $headers);
        
        $message = '<div class="alert alert-success">Sua solicitação foi recebida. Entraremos em contato em breve.</div>';
    } catch (PDOException $e) {
        $message = '<div class="alert alert-danger">Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente.</div>';
    }
}
?>

<div class="container rgpd-form">
    <h1>Exercício de Direitos RGPD</h1>
    <p>Use este formulário para exercer seus direitos sob o Regulamento Geral de Proteção de Dados.</p>
    
    <?php echo $message; ?>
    
    <form method="POST" action="">
        <div class="form-group">
            <label for="nome">Nome Completo:</label>
            <input type="text" id="nome" name="nome" required class="form-control">
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required class="form-control">
        </div>
        
        <div class="form-group">
            <label for="direito">Direito a Exercer:</label>
            <select id="direito" name="direito" required class="form-control">
                <option value="">Selecione uma opção</option>
                <option value="acesso">Direito de Acesso aos Dados</option>
                <option value="retificacao">Direito de Retificação</option>
                <option value="apagamento">Direito ao Esquecimento (Apagamento)</option>
                <option value="portabilidade">Direito à Portabilidade dos Dados</option>
                <option value="oposicao">Direito de Oposição ao Processamento</option>
                <option value="restricao">Direito à Restrição de Processamento</