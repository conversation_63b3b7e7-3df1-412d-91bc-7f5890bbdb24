<?php
include '../db.php';
// Removemos o session_start() daqui pois já é iniciado em db.php -> includes/init.php

// Verifica se o usuário está logado e é admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../login.php");
    exit;
}

// Busca informações dos clientes
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM utilizadores WHERE role = 'cliente'");
    $total_clientes = $stmt->fetch()['total'];

    // Busca todos os clientes
    $stmt = $pdo->query("SELECT id_utilizador, nome, email, telefone, data_registo FROM utilizadores WHERE role = 'cliente' ORDER BY data_registo DESC");
    $clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $total_clientes = 0;
    $clientes = [];
    $error_message = "Erro ao buscar clientes: " . $e->getMessage();
}

// Processar ações (remover cliente)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'delete') {
        $id = $_POST['id'];
        try {
            $stmt = $pdo->prepare("DELETE FROM utilizadores WHERE id_utilizador = ? AND role = 'cliente'");
            $stmt->execute([$id]);
            $success_message = "Cliente removido com sucesso!";
        } catch (PDOException $e) {
            $error_message = "Erro ao remover cliente: " . $e->getMessage();
        }
        
        // Recarregar a página para atualizar os dados
        header("Location: clientes.php");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Clientes - Cake Garden</title>
    <link rel="stylesheet" href="../../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .clientes-container {
            padding: 20px;
        }
        
        .clientes-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .table tr:hover {
            background-color: #f5f5f5;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-card h3 {
            color: #93622B;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .stats-card p {
            color: #666;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h2>Cake Garden</h2>
                <p>Painel Admin</p>
            </div>
            <ul class="menu">
                <li><a href="dashboard.php"><i class="fa-solid fa-gauge"></i> Dashboard</a></li>
                <li><a href="bolos.php"><i class="fa-solid fa-cake-candles"></i> Elementos Personalização</a></li>
                <li><a href="pedidos.php"><i class="fa-solid fa-shopping-cart"></i> Pedidos</a></li>
                <li class="active"><a href="clientes.php"><i class="fa-solid fa-users"></i> Clientes</a></li>
                <li><a href="confeiteiros.php"><i class="fa-solid fa-chef-hat"></i> Confeiteiros</a></li>
                <li><a href="../index.php"><i class="fa-solid fa-home"></i> Ver Site</a></li>
                <li><a href="../logout.php"><i class="fa-solid fa-sign-out-alt"></i> Sair</a></li>
            </ul>
        </div>
        
        <!-- Conteúdo principal -->
        <div class="main-content">
            <div class="header">
                <h1>Gestão de Clientes</h1>
                <div class="user-info">
                    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                    <a href="../logout.php" class="logout-btn"><i class="fa-solid fa-sign-out-alt"></i></a>
                </div>
            </div>
            
            <div class="clientes-container">
                <!-- Estatísticas -->
                <div class="stats-card">
                    <h3><?php echo $total_clientes; ?></h3>
                    <p>Total de Clientes Registados</p>
                </div>
                
                <!-- Mensagens de sucesso/erro -->
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>
                
                <!-- Lista de clientes -->
                <div class="clientes-table">
                    <h3 style="padding: 20px; margin: 0; background-color: #f8f9fa; border-bottom: 1px solid #ddd;">
                        Lista de Clientes Registados
                    </h3>
                    
                    <?php if (empty($clientes)): ?>
                        <div style="padding: 20px; text-align: center; color: #666;">
                            Nenhum cliente registado ainda.
                        </div>
                    <?php else: ?>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Email</th>
                                    <th>Telefone</th>
                                    <th>Data de Registo</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($clientes as $cliente): ?>
                                    <tr>
                                        <td><?php echo $cliente['id_utilizador']; ?></td>
                                        <td><?php echo htmlspecialchars($cliente['nome']); ?></td>
                                        <td><?php echo htmlspecialchars($cliente['email']); ?></td>
                                        <td><?php echo htmlspecialchars($cliente['telefone'] ?? 'N/A'); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($cliente['data_registo'])); ?></td>
                                        <td>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Tem certeza que deseja remover este cliente?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $cliente['id_utilizador']; ?>">
                                                <button type="submit" class="btn-danger">
                                                    <i class="fa-solid fa-trash"></i> Remover
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
