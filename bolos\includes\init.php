<?php
// Arquivo de inicialização do sistema
require_once 'SessionManager.php';

// Iniciar sessão segura
SessionManager::startSecureSession();

// Forçar HTTPS em produção
if (!isset($_SERVER['HTTPS']) && getenv('ENVIRONMENT') === 'production') {
    $redirect = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    header('HTTP/1.1 301 Moved Permanently');
    header('Location: ' . $redirect);
    exit();
}

// Definir cabeçalhos de segurança
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: SAMEORIGIN');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// Em produção, adicionar Content-Security-Policy
if (getenv('ENVIRONMENT') === 'production') {
    header("Content-Security-Policy: default-src 'self'; script-src 'self' https://api.staticforms.xyz; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:;");
}
