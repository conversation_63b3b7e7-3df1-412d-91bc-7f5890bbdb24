<?php
include 'db.php';
// Removemos o session_start() daqui pois j<PERSON> é iniciado em db.php -> includes/init.php
require_once 'includes/header.php';
?>

    <style>
        .error-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .error-icon {
            font-size: 80px;
            color: #F44336;
            margin-bottom: 20px;
        }

        .error-title {
            font-family: 'Courgette', cursive;
            color: #93622B;
            font-size: 32px;
            margin-bottom: 20px;
        }

        .error-message {
            font-size: 18px;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn-retry {
            display: inline-block;
            background-color: #93622B;
            color: white;
            padding: 12px 24px;
            font-size: 18px;
            border-radius: 4px;
            text-decoration: none;
            transition: background-color 0.3s;
            margin-right: 15px;
        }

        .btn-home {
            display: inline-block;
            background-color: #555;
            color: white;
            padding: 12px 24px;
            font-size: 18px;
            border-radius: 4px;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .btn-retry:hover {
            background-color: #7a5023;
        }

        .btn-home:hover {
            background-color: #333;
        }
    </style>

    <div class="error-container">
        <i class="fas fa-exclamation-circle error-icon"></i>
        <h1 class="error-title">Ocorreu um Erro</h1>
        <p class="error-message">
            Desculpe, ocorreu um erro ao processar o seu pedido. Por favor, tente novamente ou entre em contato conosco diretamente.
            <br><br>
            Se o problema persistir, você pode nos contatar pelo telefone ou email disponíveis na página de contato.
        </p>
        <a href="loja2.php" class="btn-retry">Tentar Novamente</a>
        <a href="index.php" class="btn-home">Voltar para a Página Inicial</a>
    </div>

<?php
require_once 'includes/footer.php';
?>
