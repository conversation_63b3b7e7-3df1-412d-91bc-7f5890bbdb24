<?php
// Tratamento seguro de sessões
if (session_status() == PHP_SESSION_NONE) {
    try {
        session_start();
    } catch (Exception $e) {
        // Se houver erro na sessão, destruir e criar nova
        session_destroy();
        session_start();
    }
}

// Se a sessão estiver corrompida, limpar e reiniciar
if (isset($_SESSION) && !is_array($_SESSION)) {
    session_destroy();
    session_start();
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galeria - Cake Garden</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courgette&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lobster+Two:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!--navbar-->
    <header>
        <div class="barra">
            <div class="logo">
                <img src="../image/Cake_Garden__3_-removebg-preview.png" alt="Cake Garden Logo">
            </div>
            <!--barra de pesquisa-->


            <ul class="nav-list-icon">
                <?php if(isset($_SESSION['user_id'])): ?>
                <div class="user-initial">
                    <span><?php echo strtoupper(substr($_SESSION['user_name'], 0, 1)); ?></span>
                </div>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> sair
                </a>
                <?php else: ?>
                <a href="./login.php">
                    <i class="fa-solid fa-user"></i>
                </a>
                <?php endif; ?>
            </ul>
        </div>

        <!-- Adicionar botão de menu hambúrguer -->
        <div class="mobile-menu-toggle">
            <i class="fas fa-bars"></i>
        </div>

        <!--menu-->
        <div class="menu-bar">
            <ul id="main-menu">
                <li><a href="index.php" <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'class="active"' : ''; ?>><i class="fa-solid fa-house"></i>INÍCIO</a></li>
                <li><a href="about us.php" <?php echo basename($_SERVER['PHP_SELF']) == 'about us.php' ? 'class="active"' : ''; ?>><i class="fa-solid fa-handshake"></i>SOBRE</a></li>
                <li><a href="loja2.php" <?php echo basename($_SERVER['PHP_SELF']) == 'loja2.php' ? 'class="active"' : ''; ?>><i class="fa-solid fa-bag-shopping"></i>LOJA</a></li>
                <li style="width: 150px;"><a href="contacto.php" <?php echo basename($_SERVER['PHP_SELF']) == 'contacto.php' ? 'class="active"' : ''; ?>><i class="fa-solid fa-phone"></i>CONTACTO</a></li>
                <li><a href="blog.php" <?php echo basename($_SERVER['PHP_SELF']) == 'blog.php' ? 'class="active"' : ''; ?>><i class="fa-solid fa-pen-to-square"></i>BLOG</a></li>
                <li><a href="galeria.php" <?php echo basename($_SERVER['PHP_SELF']) == 'galeria.php' ? 'class="active"' : ''; ?>><i class="fa-solid fa-images"></i>GALERIA</a></li>
                <li style="width: 150px;"><a href="cliente.php" <?php echo basename($_SERVER['PHP_SELF']) == 'cliente.php' ? 'class="active"' : ''; ?>><i class="fa-solid fa-star"></i>CLIENTE</a></li>
                <?php if(isset($_SESSION['user_id']) && $_SESSION['user_role'] === 'admin'): ?>
                <li style="width: 150px;"><a href="admin/dashboard.php" <?php echo (strpos($_SERVER['PHP_SELF'], 'admin/') !== false) ? 'class="active"' : ''; ?>><i class="fa-solid fa-tachometer-alt"></i>DASHBOARD</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </header>
    <!-- fim do menu-->
<style>
    .galeria-container {
        max-width: 1200px;
        margin: 40px auto;
        padding: 0 20px;
    }

    .galeria-titulo {
        text-align: center;
        color: #93622B;
        font-family: 'Lobster Two', cursive;
        margin-bottom: 30px;
        font-size: 2.5rem;
    }

    .categoria-section {
        margin-bottom: 50px;
    }

    .categoria-titulo {
        color: #93622B;
        font-family: 'Playfair Display', serif;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #93622B;
    }

    /* Estilo para o grid de imagens */
    .grid-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
        
    }

    .grid-item {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .grid-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.15);
    }


    .grid-item img {
        width: 100%;
        height: 220px;
        object-fit: cover;
        display: block;
    }

    .grid-info {
        padding: 15px;
        background-color: white;
    }

    .grid-info h3 {
        margin: 0 0 8px 0;
        color: #93622B;
        font-family: 'Playfair Display', serif;
    }

    .grid-info p {
        margin: 0;
        color: #666;
        font-size: 14px;
    }

    /* Estilo para visualização em dispositivos menores */
    @media (max-width: 768px) {
        .grid-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .grid-container {
            grid-template-columns: 1fr;
        }
    }

    .decorative-line {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 30px 0;
    }

    .line {
        height: 2px;
        width: 100px;
        background-color: #93622B;
        margin: 0 15px;
    }
</style>
    <main class="galeria-container">
        <div class="decorative-line">
            <div class="line"></div>
            <h1 class="galeria-titulo">Nossa Galeria de Bolos</h1>
            <div class="line"></div>
        </div>
        
        
        <!-- Seção: Bolos Simples -->
        <section class="categoria-section">
            <h2 class="categoria-titulo">Bolos Simples</h2>
            <div class="grid-container">
                <!-- Item 1 -->
                <div class="grid-item">
                   <img src="../img_bl///bolo_coco.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo de Coco</h3>
                        
                    </div>
                </div>
                
                <!-- Item 2 -->
                <div class="grid-item">
                    <img src="../image//lemon-cake-8274419_1280.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo de limão</h3>
                      
                    </div>
                </div>
                
                <!-- Item 3 -->
                <div class="grid-item">
                   <img src="../img_bl//Bolo de chocolate rapidinho - Na Cozinha da Helo.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo de chocolate</h3>
                    
                    </div>
                </div>
                
                <!-- Item 4 -->
                <div class="grid-item">
                    <img src="../img_bl//pin bolo marmore.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo mármore</h3>
                       
                    </div>
                </div>

                 <!-- Item 5 -->
                <div class="grid-item">
                    <img src="../img_bl//Pão de Ló Caseiro_ Leve, Fácil e Fofinho!.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo de Pão de Ló</h3>
                    
                    </div>
                </div>

                 <!-- Item 6 -->
                <div class="grid-item">
                    <img src="../img_bl//bolo de laranja.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo de laranja</h3>
                    
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Seção: Bolos Recheados -->
        <section class="categoria-section">
            <h2 class="categoria-titulo">Bolos Recheados</h2>
            <div class="grid-container">
                <!-- Item 1 -->
                <div class="grid-item">
                    <img src="../img_bl//recheio de coco com leite condensado.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo com recheio de chocolate branco</h3>
                       
                    </div>
                </div>
                
                <!-- Item 2 -->
                <div class="grid-item">
                    <img src="..//image//about us.png" alt="">
                    <div class="grid-info">
                        <h3>Bolo com recheio de coco com doce de leite</h3>
                        
                    </div>
                </div>
                
                <!-- Item 3 -->
                <div class="grid-item">
                   <img src="../img_bl//recheio chantilly.jpg" alt="Bolo Recheado de Morango">
                    <div class="grid-info">
                        <h3>Bolo com recheio de chantilly</h3>
                       
                    </div>
                </div>
                
                <!-- Item 4 -->
                <div class="grid-item">
                    <img src="../img_bl//recheio de doce de leite.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo com recheio de doce de leite</h3>
                    
                    </div>
                </div>

                <!-- Item 5 -->
                <div class="grid-item">
                    <img src="../img_bl//recheio de pistacio.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo com recheio de pistácio</h3>
                        
                    </div>
                </div>


                   <!-- Item 6 -->
                <div class="grid-item">
                    <img src="../img_bl//bolo de morango 2.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo com recheio de frutos vermelhos</h3>
                       
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Seção: Bolos com Cobertura -->
        <section class="categoria-section">
            <h2 class="categoria-titulo">Bolos com Cobertura</h2>
            <div class="grid-container">

            <!-- Item 2 -->
                <div class="grid-item">
                    <img src="../img_bl//cobertura de choco.webp" alt="">
                    <div class="grid-info">
                        <h3>Bolo com cobertura de pistácio</h3>
                       
                    </div>
                </div>
                
               
                <!-- Item 2 -->
                <div class="grid-item">
                    <img src="../img_bl//cobertura pistacio 2.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo com cobertura de pistácio</h3>
                       
                    </div>
                </div>
                
                <!-- Item 3 -->
                <div class="grid-item">
                    <img src="../img_bl//cobertura caramelo 2.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo com cobertura de caramelo</h3>
                       
                    </div>
                </div>
                
                <!-- Item 4 -->
                <div class="grid-item">
                    <img src="../img_bl//cobertura de chantilly.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo com cobertura de chantilly</h3>
                       
                    </div>
                </div>

                   <!-- Item 5 -->
                <div class="grid-item">
                    <img src="../img_bl//cobertura de fondant.jpg" alt="">
                    <div class="grid-info">
                        <h3>Bolo com cobertura de fondant</h3>
                       
                    </div>
                </div>

                    <!-- Item 6 -->
                <div class="grid-item">
                    <img src="../img_bl//cobertura de merengue amargo.avif" alt="">
                    <div class="grid-info">
                        <h3>Bolo com cobertura de merengue italiano</h3>
                       
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Seção: Bolos Personalizados -->
        <section class="categoria-section">
            <h2 class="categoria-titulo">Bolos Personalizados</h2>
            <div class="grid-container">
                <!-- Item 1 -->
                <div class="grid-item">
                    <img src="../img_bl//personalizado momo.webp" alt="">
                    <div class="grid-info">
                        <h3>Bolo Infantil</h3>
                    </div>
                </div>
                
                <!-- Item 2 -->
                <div class="grid-item">
                    <img src="..//img_bl//Bolo lilás.jpeg"  alt="">
                    <div class="grid-info">
                        <h3>Bolo de Casamento</h3>
                    </div>
                </div>
                
                <!-- Item 3 -->
                <div class="grid-item">
                    <img src="..//img_bl//Cake bautismo angelito.jpeg" alt="Bolo Personalizado Temático">
                    <div class="grid-info">
                        <h3>Bolo Temático</h3>
                    </div>
                </div>
                
                <!-- Item 4 -->
                <div class="grid-item">
                    <img src="..//img_bl//Cake.jpeg" alt="Bolo Personalizado Corporativo">
                    <div class="grid-info">
                        <h3>Bolo Corporativo</h3>
                    </div>
                </div>

                <!-- Item 5 -->
                <div class="grid-item">
                    <img src="..//img_bl//boloHa.jpg" alt="Bolo Personalizado Corporativo">
                    <div class="grid-info">
                        <h3>Bolo Corporativo</h3>
                    </div>
                </div>

                <!-- Item 6 -->
                <div class="grid-item">
                    <img src="..//img_bl//Bolo decorado com frutas e chocolate.png" alt="Bolo Personalizado Corporativo">
                    <div class="grid-info">
                        <h3>Bolo Corporativo</h3>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php require_once 'includes/footer.php'; ?>
</body>
</html>
