<?php
require_once 'db.php'; // Inclui a conexão com o banco de dados

// Consultar tipos de bolos
$query_tipos = $pdo->query("SELECT * FROM tipo_bolo");
$tipos_bolo = $query_tipos->fetchAll(PDO::FETCH_ASSOC);

// Consultar massas
$query_massas = $pdo->query("SELECT * FROM massas");
$massas = $query_massas->fetchAll(PDO::FETCH_ASSOC);

// Consultar coberturas
$query_coberturas = $pdo->query("SELECT * FROM coberturas");
$coberturas = $query_coberturas->fetchAll(PDO::FETCH_ASSOC);

// Consultar recheios
$query_recheios = $pdo->query("SELECT * FROM recheios");
$recheios = $query_recheios->fetchAll(PDO::FETCH_ASSOC);

// Não vamos consultar formatos para evitar o erro
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - Cake Garden</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courgette&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Lobster+Two:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    
    <style>
        .blog-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .blog-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .blog-header h1 {
            color: #93622B;
            font-family: 'Cormorant Garamond', serif;
            font-size: 42px;
            margin-bottom: 15px;
        }
        
        .blog-header p {
            color: #666;
            font-size: 18px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .combinations-section {
            background-color: #FFF8F0;
            padding: 50px 20px;
            border-radius: 10px;
            margin-bottom: 50px;
        }
        
        .combinations-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .combinations-header h2 {
            color: #93622B;
            font-family: 'Lobster Two', cursive;
            font-size: 32px;
        }
        
        .combinations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .combination-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .combination-card i {
            font-size: 40px;
            color: #93622B;
            margin-bottom: 15px;
        }
        
        .combination-card h3 {
            color: #333;
            font-family: 'Lobster Two', cursive;
            font-size: 20px;
            margin-bottom: 10px;
        }
        
        .combination-card p {
            color: #666;
            font-size: 15px;
            line-height: 1.5;
        }
        
        .tips-section {
            margin-bottom: 50px;
        }
        
        .tips-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .tips-header h2 {
            color: #93622B;
            font-family: 'Lobster Two', cursive;
            font-size: 32px;
        }
        
        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
        }
        
        .tip-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .tip-card h3 {
            color: #93622B;
            font-family: 'Lobster Two', cursive;
            font-size: 22px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .tip-card h3 i {
            margin-right: 10px;
            font-size: 24px;
        }
        
        .tip-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .promotions-section {
            background-color: #FFF8F0;
            padding: 50px 20px;
            border-radius: 10px;
            margin-bottom: 50px;
        }
        
        .promotions-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .promotions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .promotion-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 3px 15px rgba(147, 98, 43, 0.15);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .promotion-badge {
            position: absolute;
            top: 10px;
            right: -30px;
            background: #E83A3A;
            color: white;
            transform: rotate(45deg);
            padding: 5px 35px;
            font-weight: bold;
        }
        
        .month-tip {
            background-color: #93622B;
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 50px;
        }
        
        .month-tip h2 {
            font-family: 'Lobster Two', cursive;
            font-size: 28px;
            margin-bottom: 15px;
            color: white;
        }
        
        .month-tip p {
            font-size: 16px;
            line-height: 1.6;
        }
        
        .decorative-line {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .line {
            height: 2px;
            width: 100px;
            background-color: #93622B;
            margin: 0 15px;
        }
    </style>
</head>
<body>
    <!--navbar-->
    <header>
        <div class="barra">
            <div class="logo">
                <img src="../image/Cake_Garden__3_-removebg-preview.png" alt="Cake Garden Logo">
            </div>
           
            <ul class="nav-list-icon">    
                <a href="./login.php">
                    <i class="fa-solid fa-user"></i>
                </a>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> sair
                </a>
            </ul>
        </div>

        <!--menu-->
        <div class="menu-bar">
            <ul>
                <li><a href="index.php"><i class="fa-solid fa-house"></i>INÍCIO</a></li>
                <li><a href="about.php"><i class="fa-solid fa-handshake"></i>SOBRE</a></li>
                <li><a href="loja2.php"><i class="fa-solid fa-bag-shopping"></i>LOJA</a></li>
                <li style="width: 150px;"><a href="contacto.php"><i class="fa-solid fa-phone"></i>CONTACTO</a></li>
                <li><a href="blog.php" class="active"><i class="fa-solid fa-pen-to-square"></i>BLOG</a></li>
                <li><a href="galeria.php" class="active"><i class="fa-solid fa-images"></i>GALERIA</a></li>
                <li style="width: 150px;"><a href="cliente.php"><i class="fa-solid fa-star"></i>CLIENTE</a></li>
            </ul>
        </div>
    </header>
    <!-- fim do menu-->
    
    <div class="blog-container">
        <div class="blog-header">
            <div class="decorative-line">
                <div class="line"></div> 
                <h1>Blog de Confeitaria</h1>
                <div class="line"></div>
            </div>
            <p>Descubra dicas, combinações de sabores e promoções para tornar o seu bolo ainda mais especial</p>
        </div>
        
        <!-- Seção de Dicas -->
        <div class="tips-section">
            <div class="tips-header">
                <div class="decorative-line">
                    <div class="line"></div> 
                    <h2>Dicas de Confeitaria</h2>
                    <div class="line"></div>
                </div>
                <p>Conselhos práticos para melhorar os seus bolos</p>
            </div>
            
            <div class="tips-grid">
                <div class="tip-card">
                    <h3><i class="fa-solid fa-temperature-high"></i> Temperatura dos Ingredientes</h3>
                    <p>Utilize ingredientes à temperatura ambiente, especialmente ovos e manteiga. Isso garante uma mistura mais homogénea e um bolo mais fofo.</p>
                </div>
                
                <div class="tip-card">
                    <h3><i class="fa-solid fa-scale-balanced"></i> Medidas Precisas</h3>
                    <p>Na confeitaria, precisão é fundamental. Utilize copos medidores e balanças para garantir as proporções corretas dos ingredientes.</p>
                </div>
                
                <div class="tip-card">
                    <h3><i class="fa-solid fa-clock"></i> Tempo de Batimento</h3>
                    <p>Não bata demais a massa depois de adicionar a farinha, pois isso pode desenvolver o glúten e deixar o bolo borrachudo.</p>
                </div>
            </div>
        </div>

        <!-- Seção de Promoções Festivas -->
        <div class="promotions-section">
            <div class="promotions-header">
                <div class="decorative-line">
                    <div class="line"></div> 
                    <h2>Promoções Especiais para Épocas Festivas</h2>
                    <div class="line"></div>
                </div>
                <p>Celebre os momentos mais doces da vida com nossas ofertas irresistíveis!</p>
            </div>
            
            <div class="promotions-grid">
                <div class="promotion-card">
                    <div class="promotion-badge">-15%</div>
                    <i class="fa-solid fa-gift" style="font-size: 50px; color: #93622B; margin-bottom: 15px;"></i>
                    <h3>Especial de Natal</h3>
                    <p>Surpreenda sua família com nossos bolos natalinos! Sabores exclusivos de frutas cristalizadas, nozes e especiarias que trazem o verdadeiro espírito do Natal para sua mesa.</p>
                    <p style="font-weight: bold; color: #93622B; font-size: 18px;">Reserve com 7 dias de antecedência e ganhe 15% de desconto!</p>
                </div>
                
                <div class="promotion-card">
                    <div class="promotion-badge">OFERTA</div>
                    <i class="fa-solid fa-heart" style="font-size: 50px; color: #E83A3A; margin-bottom: 15px;"></i>
                    <h3>Dia dos Namorados</h3>
                    <p>Declare seu amor com nossos bolos em formato de coração! Combinações perfeitas de chocolate e frutas vermelhas que derretem o coração de quem você ama.</p>
                    <p style="font-weight: bold; color: #93622B; font-size: 18px;">Na compra do bolo, ganhe uma mini garrafa de espumante!</p>
                </div>
                
                <div class="promotion-card">
                    <div class="promotion-badge">NOVO</div>
                    <i class="fa-solid fa-egg" style="font-size: 50px; color: #93622B; margin-bottom: 15px;"></i>
                    <h3>Páscoa Doce</h3>
                    <p>Bolos temáticos de Páscoa que encantam crianças e adultos! Nossos bolos em formato de coelho e ovos de Páscoa são tão lindos quanto deliciosos.</p>
                    <p style="font-weight: bold; color: #93622B; font-size: 18px;">Encomende com antecedência e receba mini ovos de chocolate de brinde!</p>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <p style="font-size: 20px; color: #93622B; font-weight: bold; margin-bottom: 15px;">Não perca estas oportunidades doces de celebrar!</p>
                <p style="font-size: 16px; color: #666;">Entre em contato conosco para mais informações sobre nossas promoções sazonais e faça já sua encomenda.</p>
                <a href="contacto.php" style="display: inline-block; background-color: #93622B; color: white; padding: 12px 25px; border-radius: 30px; text-decoration: none; margin-top: 15px; font-weight: bold; transition: all 0.3s ease;">FALE CONOSCO</a>
            </div>
        </div>

        <!-- Dica do Mês -->
        <div class="month-tip">
            <h2>Dica do Mês: Escolha a Cobertura Certa</h2>
            <p>A cobertura não é apenas um acabamento estético, mas também um toque de explosão de sabores. Escolha coberturas que complementem o sabor da massa e do recheio para criar uma experiência harmoniosa.</p>
        </div>

        <!-- Seção de Combinações de Sabores -->
        <div class="combinations-section">
            <div class="combinations-header">
                <div class="decorative-line">
                    <div class="line"></div> 
                    <h2>Combinações de Sabores Populares</h2>
                    <div class="line"></div>
                </div>
                <p>Descubra as combinações de sabores mais amadas pelos nossos clientes</p>
            </div>
            
            <div class="combinations-grid">
                <?php
                // Criar combinações baseadas nos dados da BD
                // Vamos assumir que temos acesso às tabelas massas, recheios e coberturas
                
                // Combinações populares
                $combinacoes = [
                    [
                        'massa' => 'Chocolate',
                        'recheio' => 'Brigadeiro',
                        'cobertura' => 'Ganache de Chocolate',
                        'icon' => 'fa-solid fa-cookie',
                        'descricao' => 'Uma combinação intensa para os verdadeiros amantes de chocolate.'
                    ],
                    [
                        'massa' => 'Baunilha',
                        'recheio' => 'Doce de Leite',
                        'cobertura' => 'Chantilly',
                        'icon' => 'fa-solid fa-ice-cream',
                        'descricao' => 'A suavidade da baunilha com a doçura do doce de leite cria uma experiência clássica e irresistível.'
                    ],
                    [
                        'massa' => 'Limão',
                        'recheio' => 'Creme de Limão',
                        'cobertura' => 'Merengue Italiano',
                        'icon' => 'fa-solid fa-lemon',
                        'descricao' => 'Refrescante e cítrico, perfeito para dias quentes ou para quem prefere sabores menos doces.'
                    ],
                    [
                        'massa' => 'Cenoura',
                        'recheio' => 'Creme de Baunilha',
                        'cobertura' => 'Chocolate Meio Amargo',
                        'icon' => 'fa-solid fa-carrot',
                        'descricao' => 'O clássico bolo de cenoura com chocolate, uma combinação que nunca falha.'
                    ],
                    [
                        'massa' => 'Coco',
                        'recheio' => 'Doce de Leite',
                        'cobertura' => 'Coco Ralado',
                        'icon' => 'fa-solid fa-seedling',
                        'descricao' => 'Uma experiência tropical que transporta para praias paradisíacas.'
                    ],
                    [
                        'massa' => 'Red Velvet',
                        'recheio' => 'Cream Cheese',
                        'cobertura' => 'Cream Cheese',
                        'icon' => 'fa-solid fa-heart',
                        'descricao' => 'Elegante e sofisticado, o contraste entre a massa vermelha e o cream cheese cria uma experiência visual e gustativa única.'
                    ],
                    [
                        'massa' => 'Laranja',
                        'recheio' => 'Creme de Laranja',
                        'cobertura' => 'Calda de Laranja',
                        'icon' => 'fa-solid fa-circle',
                        'descricao' => 'Refrescante e aromático, o sabor cítrico da laranja traz uma sensação de frescor ao paladar.'
                    ],
                    [
                        'massa' => 'Chocolate',
                        'recheio' => 'Mousse de Maracujá',
                        'cobertura' => 'Ganache de Chocolate',
                        'icon' => 'fa-solid fa-seedling',
                        'descricao' => 'O contraste entre o chocolate amargo e a acidez do maracujá cria uma experiência sensorial única.'
                    ],
                    [
                        'massa' => 'Baunilha',
                        'recheio' => 'Creme de Morango',
                        'cobertura' => 'Chantilly',
                        'icon' => 'fa-solid fa-apple-whole',
                        'descricao' => 'A combinação clássica de morango com baunilha é sempre uma escolha segura e deliciosa.'
                    ],
                    [
                        'massa' => 'Chocolate',
                        'recheio' => 'Creme de Avelã',
                        'cobertura' => 'Chocolate Meio Amargo',
                        'icon' => 'fa-solid fa-cookie-bite',
                        'descricao' => 'O sabor da avelã complementa perfeitamente o chocolate, criando uma experiência rica e sofisticada.'
                    ]
                ];
                
                foreach ($combinacoes as $combinacao):
                ?>
                <div class="combination-card">
                    <i class="<?php echo $combinacao['icon']; ?>"></i>
                    <h3><?php echo $combinacao['massa'] . ' & ' . $combinacao['recheio']; ?></h3>
                    <p><strong>Cobertura:</strong> <?php echo $combinacao['cobertura']; ?></p>
                    <p><?php echo $combinacao['descricao']; ?></p>
                </div>
                <?php endforeach; ?>
            </div>
        </div>